#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字库功能测试脚本
测试新增的字库制作和文字识别功能
"""

import os
import sys
import cv2
import numpy as np
from zhaoTuZhaoSe import ImageColorScript, FontLibraryCreator, FontLibraryManager

def test_font_library_creator():
    """测试字库制作功能"""
    print("测试字库制作功能...")
    
    try:
        creator = FontLibraryCreator()
        
        # 创建测试字符图像
        test_img = np.zeros((50, 200), dtype=np.uint8)
        cv2.putText(test_img, "TEST", (10, 35), cv2.FONT_HERSHEY_SIMPLEX, 1, 255, 2)
        cv2.imwrite("test_text.png", test_img)
        
        # 测试字符分割预览
        boxes = creator.preview_segmentation("test_text.png", "preview_segmentation.png")
        print(f"   检测到 {len(boxes)} 个字符区域")
        
        # 手动添加字符到字库
        char_img = np.ones((20, 15), dtype=np.uint8) * 255
        cv2.rectangle(char_img, (2, 2), (12, 17), 0, 2)
        creator.add_character_to_library("O", char_img)
        
        # 保存字库
        success = creator.save_library("test_creator.json", "测试字库")
        print(f"   字库保存：{'成功' if success else '失败'}")
        
        return success
        
    except Exception as e:
        print(f"   字库制作测试失败：{e}")
        return False

def test_font_library_manager():
    """测试字库管理功能"""
    print("测试字库管理功能...")
    
    try:
        manager = FontLibraryManager()
        
        # 创建两个测试字库
        creator1 = FontLibraryCreator()
        creator1.add_character_to_library("A", np.ones((20, 15), dtype=np.uint8) * 255)
        creator1.save_library("test_lib1.json", "测试字库1")
        
        creator2 = FontLibraryCreator()
        creator2.add_character_to_library("B", np.ones((20, 15), dtype=np.uint8) * 255)
        creator2.save_library("test_lib2.json", "测试字库2")
        
        # 测试加载字库
        load1 = manager.load_library("test_lib1.json", "lib1")
        load2 = manager.load_library("test_lib2.json", "lib2")
        print(f"   字库加载：lib1 {'成功' if load1 else '失败'}, lib2 {'成功' if load2 else '失败'}")
        
        # 测试合并字库
        merge_success = manager.merge_libraries(["lib1", "lib2"], "merged")
        print(f"   字库合并：{'成功' if merge_success else '失败'}")
        
        # 测试字库分析
        analysis = manager.analyze_library("merged")
        print(f"   合并后字库包含 {analysis.get('total_characters', 0)} 个字符")
        
        # 测试字库优化
        optimize_success = manager.optimize_library("merged")
        print(f"   字库优化：{'成功' if optimize_success else '失败'}")
        
        # 测试列出字符
        characters = manager.list_characters("merged")
        print(f"   字库字符：{characters}")
        
        return True
        
    except Exception as e:
        print(f"   字库管理测试失败：{e}")
        return False

def test_text_recognition():
    """测试文字识别功能"""
    print("测试文字识别功能...")
    
    try:
        # 创建包含文字的测试图像
        test_screen = np.ones((300, 400, 3), dtype=np.uint8) * 255
        cv2.putText(test_screen, "Hello", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        cv2.putText(test_screen, "World", (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        cv2.imwrite("test_screen.png", test_screen)
        
        # 创建字库
        creator = FontLibraryCreator()
        
        # 手动创建一些字符模板（简化版本）
        h_img = np.zeros((40, 30), dtype=np.uint8)
        cv2.putText(h_img, "H", (5, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, 255, 2)
        creator.add_character_to_library("H", h_img)
        
        e_img = np.zeros((40, 30), dtype=np.uint8)
        cv2.putText(e_img, "e", (5, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, 255, 2)
        creator.add_character_to_library("e", e_img)
        
        creator.save_library("test_recognition.json", "识别测试字库")
        
        # 测试文字识别
        script = ImageColorScript()
        load_success = script.load_font_library("test_recognition.json")
        print(f"   字库加载到识别引擎：{'成功' if load_success else '失败'}")
        
        if load_success:
            print("   文字识别功能已就绪")
            print("   可以使用以下方法进行测试：")
            print("   - script.find_character('H')")
            print("   - script.find_text('Hello')")
            print("   注意：需要屏幕上有对应的文字才能找到")
        
        return True
        
    except Exception as e:
        print(f"   文字识别测试失败：{e}")
        return False

def test_integration():
    """集成测试"""
    print("集成测试...")
    
    try:
        # 创建完整的工作流程
        print("   1. 创建字库...")
        creator = FontLibraryCreator()
        
        # 添加一些测试字符
        for i, char in enumerate("ABCD1234"):
            char_img = np.zeros((30, 25), dtype=np.uint8)
            cv2.putText(char_img, char, (5, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.8, 255, 2)
            creator.add_character_to_library(char, char_img, {"index": i})
        
        creator.save_library("integration_test.json", "集成测试字库")
        
        print("   2. 管理字库...")
        manager = FontLibraryManager()
        manager.load_library("integration_test.json", "integration")
        
        analysis = manager.analyze_library("integration")
        print(f"      字库包含 {analysis.get('total_characters', 0)} 个字符")
        
        print("   3. 文字识别...")
        script = ImageColorScript()
        script.load_font_library("integration_test.json")
        
        print("   ✓ 集成测试完成")
        return True
        
    except Exception as e:
        print(f"   集成测试失败：{e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        "test_text.png",
        "preview_segmentation.png",
        "test_creator.json",
        "test_lib1.json",
        "test_lib2.json",
        "test_screen.png",
        "test_recognition.json",
        "integration_test.json"
    ]
    
    for file in test_files:
        try:
            if os.path.exists(file):
                os.remove(file)
        except:
            pass

def main():
    """主测试函数"""
    print("=" * 60)
    print("字库功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("字库制作", test_font_library_creator()))
    test_results.append(("字库管理", test_font_library_manager()))
    test_results.append(("文字识别", test_text_recognition()))
    test_results.append(("集成测试", test_integration()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:12} : {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！字库功能工作正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    print("\n功能说明：")
    print("1. 字库制作：从图像提取字符，创建字库文件")
    print("2. 字库管理：加载、保存、合并、优化字库")
    print("3. 文字识别：在屏幕上查找字符和文字串")
    print("4. 集成测试：完整工作流程验证")
    
    # 清理测试文件
    cleanup_test_files()
    print("\n测试文件已清理。")

if __name__ == "__main__":
    main()

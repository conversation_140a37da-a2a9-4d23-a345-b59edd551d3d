# 导入cv2库
import cv2
import numpy as np

# 读取图片为灰度图像
# 参数为图片路径，返回值为灰度图像
image_gray = cv2.imread('desktop.png', cv2.IMREAD_GRAYSCALE)

# 读取模板图片为灰度图像
# 参数为图片路径，返回值为灰度图像
template_gray = cv2.imread('kwMusic.png', cv2.IMREAD_GRAYSCALE)

# 使用cv2.matchTemplate函数进行模板匹配
# 参数为待匹配图像和模板图像，返回值为匹配结果的坐标
# 匹配方法为cv2.TM_CCOEFF_NORMED，表示使用相关系数作为匹配度量
result = cv2.matchTemplate(image_gray, template_gray, cv2.TM_CCOEFF_NORMED)

# 使用cv2.minMaxLoc函数找到匹配结果中的最大值和最小值，以及它们的位置
min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

# # 在原始图像中标记出匹配结果的位置
# # 参数为原始图像、矩形左上角的坐标、矩形右下角的坐标、矩形颜色、矩形线条粗细
# cv2.rectangle(image_gray, max_loc, (max_loc[0] + template_gray.shape[1], max_loc[1] + template_gray.shape[0]),
#               (0, 0, 255), 2)

# 如果匹配到多个结果，可以使用以下代码标记出所有结果的位置
# threshold为匹配度的阈值，可以根据实际情况调整
threshold = 0.9
loc = np.where(result >= threshold)
for pt in zip(*loc[::-1]):
    cv2.rectangle(image_gray, pt, (pt[0] + template_gray.shape[1], pt[1] + template_gray.shape[0]), (0, 0, 255), 2)




cv2.imshow('result', image_gray)
cv2.waitKey(0)
cv2.destroyAllWindows()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接修复方案
基于观察到的问题直接提供解决方案
"""

from image_color_script import ImageColorScript
import cv2
import numpy as np

def direct_solution():
    """直接解决方案"""
    print("=== 直接解决方案 ===")
    print("基于分析结果的直接修复")
    print()
    
    script = ImageColorScript()
    screen_img = script.capture_screen()
    template_path = "template.bmp"
    
    # 问题分析：
    # 1. 找到的最接近位置是(265, 206)，距离正确位置(242, 212)只有29px
    # 2. 这说明算法在正确区域，但可能需要更密集的搜索
    
    print("🔍 方案1: 增加搜索密度和候选数量")
    
    # 大幅增加候选数量，降低阈值
    result1 = script.find_all_matches_hybrid(
        screen_img,
        template_path,
        template_threshold=0.2,    # 大幅降低
        mse_threshold=0.7,         # 大幅降低
        max_template_results=200,  # 大幅增加候选数量
        max_final_results=50       # 返回更多结果
    )
    
    if result1:
        print(f"找到 {len(result1)} 个候选")
        
        # 寻找最接近正确位置的候选
        best_candidate = None
        best_distance = float('inf')
        
        print("前20个候选:")
        for i, (x, y, sim) in enumerate(result1[:20]):
            distance = abs(x - 242) + abs(y - 212)
            status = "🎯 很接近!" if distance < 10 else "✅ 接近" if distance < 30 else "⚠️ 一般" if distance < 50 else "❌ 较远"
            print(f"  候选{i+1}: ({x}, {y}) MSE={sim:.3f} 距离={distance}px {status}")
            
            if distance < best_distance:
                best_distance = distance
                best_candidate = (x, y, sim)
        
        if best_candidate:
            x, y, sim = best_candidate
            print(f"\n🏆 最佳候选: ({x}, {y}) MSE={sim:.3f} 距离={best_distance}px")
            
            if best_distance < 15:
                print("✅ 找到了很接近的位置!")
            
            # 保存结果
            script.save_marked_image(
                screen_img,
                "direct_solution_result.png",
                best_candidate,
                template_path,
                color=(0, 255, 0),
                show_info=True
            )
            print("💾 结果已保存: direct_solution_result.png")
    
    print(f"\n🔍 方案2: 纯Template方法（更低阈值）")
    
    # 尝试纯template方法
    template_results = script.find_similar_regions(
        screen_img,
        template_path,
        method='template',
        threshold=0.2,  # 很低的阈值
        max_results=50
    )
    
    if template_results:
        print(f"Template方法找到 {len(template_results)} 个候选")
        
        best_template = None
        best_template_distance = float('inf')
        
        print("前10个Template候选:")
        for i, (x, y, w, h) in enumerate(template_results[:10]):
            distance = abs(x - 242) + abs(y - 212)
            status = "🎯 很接近!" if distance < 10 else "✅ 接近" if distance < 30 else "❌ 较远"
            print(f"  Template{i+1}: ({x}, {y}) 距离={distance}px {status}")
            
            if distance < best_template_distance:
                best_template_distance = distance
                best_template = (x, y, w, h)
        
        if best_template and best_template_distance < 20:
            x, y, w, h = best_template
            print(f"\n🏆 最佳Template结果: ({x}, {y}) 距离={best_template_distance}px")
    
    print(f"\n🔍 方案3: 手动验证正确位置")
    
    # 手动检查(242, 212)位置的相似度
    template = cv2.imread(template_path)
    if template is not None:
        h, w = template.shape[:2]
        correct_x, correct_y = 242, 212
        
        # 检查边界
        if (correct_x + w <= screen_img.shape[1] and 
            correct_y + h <= screen_img.shape[0]):
            
            correct_region = screen_img[correct_y:correct_y+h, correct_x:correct_x+w]
            mse_sim = script._compare_mse(correct_region, template)
            
            print(f"正确位置({correct_x}, {correct_y})的MSE相似度: {mse_sim:.3f}")
            
            if mse_sim >= 0.7:
                print("✅ 正确位置的相似度足够高，应该能被找到")
                print("建议使用以下参数:")
                print(f"  template_threshold=0.2")
                print(f"  mse_threshold={mse_sim - 0.1:.2f}")
            else:
                print("⚠️ 正确位置的相似度较低，可能模板不完全匹配")
                print(f"建议降低mse_threshold到 {mse_sim - 0.05:.2f}")
            
            # 保存对比图像
            comparison = np.hstack([template, correct_region])
            cv2.imwrite("template_vs_correct_position.png", comparison)
            print("💾 模板对比图像已保存: template_vs_correct_position.png")

def create_final_optimized_search():
    """创建最终优化的搜索函数"""
    print(f"\n=== 最终优化搜索 ===")
    
    script = ImageColorScript()
    screen_img = script.capture_screen()
    
    print("🚀 使用最优化的参数组合:")
    
    # 基于观察到的问题，使用最宽松的参数
    final_result = script.smart_find_image(
        screen_img,
        "template.bmp",
        mode='best',
        template_threshold=0.15,  # 极低阈值
        mse_threshold=0.65        # 极低阈值
    )
    
    print(f"最终结果: {final_result}")
    
    if final_result:
        x, y, sim = final_result
        distance = abs(x - 242) + abs(y - 212)
        print(f"位置: ({x}, {y}) MSE={sim:.3f}")
        print(f"距离正确位置: {distance}px")
        
        if distance < 20:
            print("🎉 成功找到接近正确的位置!")
        else:
            print("⚠️ 仍有偏差，可能需要检查模板图像")
        
        # 保存最终结果
        script.save_marked_image(
            screen_img,
            "final_optimized_result.png",
            final_result,
            "template.bmp",
            color=(255, 0, 0),  # 红色标记
            show_info=True
        )
        print("💾 最终结果已保存: final_optimized_result.png")
    else:
        print("❌ 即使使用极低阈值也未找到匹配")
        print("建议检查:")
        print("1. 模板图像是否正确")
        print("2. 模板图像是否清晰")
        print("3. 屏幕内容是否发生变化")

def provide_code_solution():
    """提供代码解决方案"""
    print(f"\n=== 代码解决方案 ===")
    
    solution_code = '''
# 解决方案1: 使用极低阈值
script = ImageColorScript()
screen_img = script.capture_screen()

result = script.smart_find_image(
    screen_img,
    "template.bmp",
    mode='best',
    template_threshold=0.15,  # 极低阈值
    mse_threshold=0.65        # 极低阈值
)

print("极低阈值结果:", result)

# 解决方案2: 获取大量候选进行分析
all_candidates = script.find_all_matches_hybrid(
    screen_img,
    "template.bmp",
    template_threshold=0.2,
    mse_threshold=0.7,
    max_template_results=200,  # 大量候选
    max_final_results=50
)

if all_candidates:
    # 找到最接近(242, 212)的候选
    best = min(all_candidates, 
               key=lambda m: abs(m[0] - 242) + abs(m[1] - 212))
    print("最接近的候选:", best)

# 解决方案3: 纯Template方法作为备选
template_results = script.find_similar_regions(
    screen_img,
    "template.bmp",
    method='template',
    threshold=0.2,
    max_results=20
)

if template_results:
    best_template = min(template_results,
                       key=lambda m: abs(m[0] - 242) + abs(m[1] - 212))
    print("最接近的Template结果:", best_template)
    '''
    
    print("💡 推荐使用的代码:")
    for line in solution_code.strip().split('\n'):
        print(f"  {line}")

def main():
    """主函数"""
    print("=" * 50)
    print("直接修复搜索问题")
    print("=" * 50)
    print("基于观察: 最接近结果(265, 206)距离目标(242, 212)只有29px")
    print("策略: 增加搜索密度，降低阈值，增加候选数量")
    print("=" * 50)
    
    direct_solution()
    create_final_optimized_search()
    provide_code_solution()
    
    print(f"\n" + "=" * 50)
    print("🎯 关键建议:")
    print("1. 使用template_threshold=0.15, mse_threshold=0.65")
    print("2. 增加max_template_results到200")
    print("3. 如果还不行，检查模板图像质量")
    print("4. 考虑使用纯template方法作为备选")

if __name__ == "__main__":
    main()

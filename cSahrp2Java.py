import re
import os


class EnumConverter:
    def __init__(self):
        # 匹配类定义
        self.class_pattern = re.compile(r'public class (\w+)\s*\{')
        # 匹配 XML 注释
        self.comment_pattern = re.compile(r'/// <summary>\s*/// (.*?)\s*/// </summary>')
        # 匹配常量定义
        self.const_pattern = re.compile(r'public const string (\w+) = "(.*?)";')
        # 匹配数值型常量
        self.num_const_pattern = re.compile(r'public const int (\w+) = (\d+);')
        # 区域标记
        self.region_pattern = re.compile(r'#region.*|#endregion')

    def parse_csharp_file(self, content):
        """解析 C# 文件内容"""
        enums = []
        pos = 0

        while True:
            class_match = self.class_pattern.search(content, pos)
            if not class_match:
                break

            class_name = class_match.group(1)
            start_idx = content.find('{', class_match.end())
            brace_count = 1
            pos = start_idx + 1

            # 寻找类结束位置（匹配括号）
            while brace_count > 0 and pos < len(content):
                char = content[pos]
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                pos += 1

            if brace_count != 0:
                continue  # 跳过不完整的类定义

            class_content = content[start_idx + 1:pos - 1]
            enum_items = self.parse_class_content(class_content)

            if enum_items:
                enums.append({
                    'name': class_name,
                    'items': enum_items
                })

        return enums

    def parse_class_content(self, content):
        """解析类内部内容"""
        items = []

        # 处理 XML 注释
        comments = {}
        for comment_match in self.comment_pattern.finditer(content):
            const_start = content.find('public const', comment_match.end())
            if const_start != -1:
                const_match = re.match(r'public const string (\w+)', content[const_start:])
                if const_match:
                    comments[const_match.group(1)] = comment_match.group(1)

        # 处理字符串常量
        for name_match, value in self.const_pattern.findall(content):
            items.append({
                'name': name_match,
                'value': value,
                'type': 'string',
                'comment': comments.get(name_match, '')
            })

        # 处理数值常量
        for name_match, value in self.num_const_pattern.findall(content):
            items.append({
                'name': name_match,
                'value': value,
                'type': 'int',
                'comment': comments.get(name_match, '')
            })

        return items

    def generate_java_enum(self, enum_data):
        """生成 Java 枚举代码"""
        java_code = f"/**\n * {enum_data['name']} 枚举\n */\n"
        java_code += f"public enum {enum_data['name']} {{\n"

        # 枚举项
        for i, item in enumerate(enum_data['items']):
            comment = ''
            if item['comment']:
                comment = f"    /** {item['comment']} */\n"

            line = f"    {item['name']}"
            if i < len(enum_data['items']) - 1:
                line += ","
            else:
                line += ";"

            java_code += comment + line + "\n"

        # 值字段和构造方法
        has_string_values = any(item['type'] == 'string' for item in enum_data['items'])
        if has_string_values:
            java_code += """
    private final String value;

    private {}(String value) {{
        this.value = value;
    }}

    public String getValue() {{
        return value;
    }}
""".format(enum_data['name'])
        else:
            java_code += """
    private final int code;

    private {}(int code) {{
        this.code = code;
    }}

    public int getCode() {{
        return code;
    }}
""".format(enum_data['name'])

        java_code += "}\n"
        return java_code

    def convert(self, input_text, output_dir="output"):
        """执行转换"""
        # 移除区域标记
        clean_text = self.region_pattern.sub('', input_text)
        # 解析枚举
        enums = self.parse_csharp_file(clean_text)

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 生成 Java 文件
        for enum_data in enums:
            java_code = self.generate_java_enum(enum_data)
            with open(os.path.join(output_dir, f"{enum_data['name']}.java"), "w") as f:
                f.write(java_code)

        print(f"成功转换 {len(enums)} 个枚举类")


# 使用示例
if __name__ == "__main__":
    converter = EnumConverter()

    # 读取输入文件
    with open("Pasted_Text_1746005010478.txt", "r", encoding='utf-8') as f:
        csharp_code = f.read()

    converter.convert(csharp_code)
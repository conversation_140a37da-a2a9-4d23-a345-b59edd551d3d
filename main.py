# selenium 4

# undeceted-chromedriver
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.core.utils import get_browser_version_from_os
import requests, re, time, os


# browserVersion = get_browser_version_from_os("google-chrome")  # 获取当前系统chrome浏览器的版本号
# mainBrowserVersion = browserVersion.split(".")[0]  # 获取浏览器的主版本号
# resp = requests.get(url="https://chromedriver.storage.googleapis.com/")
# content = resp.text
# availableVersionList = re.search(f"({mainBrowserVersion}\.\d+\.\d+\.\d+)/chromedriver_win32\.zip.*?", content, re.S)
# if availableVersionList == None:
#     print(f"镜像网站上没有找到主版本号为{mainBrowserVersion}的chromedriver文件，请核实！")
#     time.sleep(10)
#     os._exit(0)
# else:
#     availableVersion = availableVersionList.group(1)
# driver_path = ChromeDriverManager(
#     version=availableVersion).install()  # 找到镜像网站中主版本号与chrome主版本一致的，将匹配到的第一个完整版本号的chromedriver下载使用

url = 'https://swapp.singlewindow.cn/deskserver/sw/deskIndex?menu_id=npts'
option = webdriver.ChromeOptions()
# 以开发者模式 右上角会提示：请停用开发者模式运行的扩展程序，不能点击停用
option.add_experimental_option('excludeSwitches', ['enable-automation'])
# 禁用自动化扩展
option.add_experimental_option('useAutomationExtension', False)
# 禁止浏览器显示正在被控制
option.add_argument("--disable-blink-features=AutomationControlled")

browser = webdriver.Chrome(options=option)

# 读取文件
with open('stealth.min.js', 'r',encoding='utf-8') as f:
    js = f.read()
# 调用函数在页面加载前执行脚本
browser.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {'source': js})

# 打开地址
browser.get(url)
# 最大化窗口
browser.maximize_window()

# browser.get("https://swapp.singlewindow.cn:443/nptsserver/sw/ems/npts/queryEml?sysId=B1&ngBasePath=https%3A%2F%2Fswapp.singlewindow.cn%3A443%2Fnptsserver%2F")
#
# for i in "5025940494":
#     browser.find_element(by="id", value="selTradeCode").send_keys(i)
#     time.sleep(.2)
#
# time.sleep(1)
# browser.find_element_by_id("btn-search").click()
# 休眠300秒
time.sleep(500)
# 关闭
browser.close()
browser.quit()

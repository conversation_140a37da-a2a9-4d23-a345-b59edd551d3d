# 找图找色脚本 (zhaoTuZhaoSe)

一个功能完整的Python图色脚本，支持屏幕截图、找图、找色、多点找色、字库制作和文字识别等功能。

## 功能特性

### 基础功能
- 🖼️ **屏幕截图**: 全屏或区域截图
- 🔍 **找图功能**: 在屏幕上查找指定图像
- 🎨 **找色功能**: 查找指定颜色的像素点
- 🎯 **多点找色**: 基于多个颜色点的模式匹配
- 🖱️ **鼠标键盘操作**: 点击、拖拽、输入文本等
- ⏱️ **等待功能**: 等待图像或颜色出现

### 高级功能
- 📚 **字库制作**: 从图像中提取字符创建字库
- 🔤 **文字识别**: 基于字库的屏幕文字识别
- 📊 **字库管理**: 加载、保存、合并、优化字库
- 🖼️ **图像比较**: 多种算法比较图像相似度
- 🎨 **现代化UI**: 基于tkinter的图形界面

## 安装依赖

```bash
pip install opencv-python numpy mss pyautogui pillow
```

## 使用方法

### 1. 命令行模式

```bash
# 运行演示程序
python zhaoTuZhaoSe.py
```

### 2. 图形界面模式

```bash
# 启动UI界面
python font_library_ui.py
```

### 3. 编程使用

```python
from zhaoTuZhaoSe import ImageColorScript, FontLibraryCreator, FontLibraryManager

# 创建脚本实例
script = ImageColorScript()

# 截图
script.save_screenshot("screenshot.png")

# 找图
positions = script.find_image("template.png", threshold=0.8)

# 找色
color_positions = script.find_color((255, 0, 0), tolerance=10)

# 多点找色
pattern = [(0, 0, (255, 255, 255)), (10, 0, (0, 0, 0))]
result = script.find_multi_color(pattern)

# 鼠标操作
script.click(100, 100)
script.type_text("Hello World")
```

## 字库功能

### 创建字库

```python
from zhaoTuZhaoSe import FontLibraryCreator

creator = FontLibraryCreator()

# 从图像创建字库
creator.create_from_image("text_image.png", "示例文字")
creator.save_library("my_font.json", "我的字库")
```

### 使用字库识别文字

```python
from zhaoTuZhaoSe import ImageColorScript

script = ImageColorScript()

# 加载字库
script.load_font_library("my_font.json")

# 查找字符
positions = script.find_character("字")

# 查找文字串
text_positions = script.find_text("示例文字")
```

## UI界面功能

### 字库制作模块
- 📁 加载图像文件
- ✂️ 自动字符分割
- 🏷️ 字符标注
- 💾 字库保存

### 字库管理模块
- 📂 加载/保存字库
- 🔄 合并多个字库
- 🗜️ 字库优化去重
- 📊 字库统计分析

### 屏幕找字模块
- 🔍 实时文字查找
- 📍 结果定位显示
- 🎯 点击查找结果
- 📷 搜索区域预览

## 文件结构

```
├── zhaoTuZhaoSe.py          # 核心功能模块
├── font_library_ui.py       # UI界面
├── button_style_demo.py     # 按钮样式演示
├── README.md               # 说明文档
└── sample_digits.json      # 示例数字字库
```

## 核心类说明

### ImageColorScript
主要的图色脚本类，包含所有基础功能：
- 屏幕截图和图像处理
- 找图、找色、多点找色
- 鼠标键盘操作
- 文字识别功能

### FontLibraryCreator
字库制作类：
- 从图像提取字符
- 字符特征提取
- 字库文件生成

### FontLibraryManager
字库管理类：
- 字库加载和保存
- 字库合并和优化
- 字库统计分析

## 示例代码

### 基础找图找色

```python
from zhaoTuZhaoSe import ImageColorScript

script = ImageColorScript()

# 全屏截图
script.save_screenshot("full_screen.png")

# 区域截图
script.save_screenshot("region.png", region=(0, 0, 500, 300))

# 找图
results = script.find_image("button.png", threshold=0.8)
if results:
    x, y, w, h = results[0]
    center_x, center_y = x + w//2, y + h//2
    script.click(center_x, center_y)

# 找色
white_pixels = script.find_color((255, 255, 255), max_results=10)
print(f"找到 {len(white_pixels)} 个白色像素")
```

### 字库制作和使用

```python
from zhaoTuZhaoSe import FontLibraryCreator, ImageColorScript

# 1. 制作字库
creator = FontLibraryCreator()

# 从多个图像创建字库
image_text_pairs = [
    ("number1.png", "123"),
    ("number2.png", "456"),
    ("text1.png", "确定取消")
]

creator.create_from_multiple_images(image_text_pairs)
creator.save_library("game_font.json", "游戏字库")

# 2. 使用字库
script = ImageColorScript()
script.load_font_library("game_font.json")

# 查找数字
positions = script.find_character("1")
if positions:
    print(f"找到数字1，位置: {positions[0]}")

# 查找文字
text_pos = script.find_text("确定")
if text_pos:
    x, y, w, h = text_pos[0]
    script.click(x + w//2, y + h//2)  # 点击确定按钮
```

## 注意事项

1. **权限要求**: 脚本需要屏幕截图和鼠标键盘控制权限
2. **性能优化**: 大量查找操作时建议限制搜索区域
3. **字库质量**: 字库质量直接影响文字识别准确率
4. **坐标系统**: 使用屏幕绝对坐标系统

## 更新日志

### v2.0.0
- ✨ 新增字库制作功能
- ✨ 新增文字识别功能
- ✨ 新增现代化UI界面
- 🔧 优化图像处理算法
- 📚 完善文档和示例

### v1.0.0
- 🎉 初始版本发布
- 📸 基础截图功能
- 🔍 找图找色功能
- 🖱️ 鼠标键盘操作

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

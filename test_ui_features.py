#!/usr/bin/env python3
"""
测试UI新功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk

def create_test_image(text, color):
    """创建测试图片"""
    img = np.zeros((100, 100, 3), dtype=np.uint8)
    img[:, :] = color
    cv2.putText(img, text, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    return img

def test_preview_layout():
    """测试预览布局"""
    root = tk.Tk()
    root.title("测试模板图片预览布局")
    root.geometry("900x600")
    
    # 主框架
    main_frame = ttk.Frame(root, padding=10)
    main_frame.pack(fill="both", expand=True)
    
    # 模拟脚本工作流界面布局
    script_frame = ttk.LabelFrame(main_frame, text="Script Workflow", padding=10)
    script_frame.pack(fill="both", expand=True)
    
    # 按钮区域
    buttons_frame = ttk.Frame(script_frame)
    buttons_frame.pack(fill="x", pady=(0, 10))
    
    ttk.Button(buttons_frame, text="➕ 添加动作").pack(side=tk.LEFT, padx=3)
    ttk.Button(buttons_frame, text="▶️ 运行工作流").pack(side=tk.LEFT, padx=3)
    ttk.Button(buttons_frame, text="⏱️ 批量设置间隔").pack(side=tk.LEFT, padx=3)
    
    # 主要内容区域 - 水平分割
    content_frame = ttk.Frame(script_frame)
    content_frame.pack(fill="both", expand=True, pady=5)
    
    # 左侧：脚本文本区域
    left_frame = ttk.Frame(content_frame)
    left_frame.pack(side=tk.LEFT, fill="both", expand=True, padx=(0, 5))
    
    script_text = tk.Text(left_frame, height=15, width=50, state="normal")
    script_text.pack(fill="both", expand=True)
    
    # 添加测试步骤
    test_steps = [
        "Step 1: [模板: 登录按钮] Find '登录按钮', Offset(10,20), Action: Left Click, Wait: 1.0s",
        "Step 2: [模板: 用户名输入框] Find '用户名输入框', Offset(0,0), Action: Left Click, Wait: 0.5s",
        "Step 3: [模板: 密码输入框] Find '密码输入框', Offset(0,0), Action: Left Click, Wait: 0.5s",
        "Step 4: [模板: 确认按钮] Find '确认按钮', Offset(5,5), Action: Left Click, Wait: 2.0s"
    ]
    
    for step in test_steps:
        script_text.insert(tk.END, step + "\n")
    
    # 右侧：模板图片预览区域
    right_frame = ttk.LabelFrame(content_frame, text="模板图片预览", padding=5)
    right_frame.pack(side=tk.RIGHT, fill="y", padx=(5, 0))
    right_frame.config(width=250)
    right_frame.pack_propagate(False)
    
    preview_label = ttk.Label(right_frame, text="点击步骤查看模板图片", 
                             anchor="center", background="white", relief="sunken")
    preview_label.pack(fill="both", expand=True, padx=5, pady=5)
    
    # 创建测试图片
    test_images = [
        create_test_image("Login", [100, 150, 200]),
        create_test_image("User", [150, 100, 200]),
        create_test_image("Pass", [200, 100, 150]),
        create_test_image("OK", [100, 200, 150])
    ]
    
    def on_text_click(event):
        """处理文本点击事件"""
        try:
            # 获取点击位置的行号
            index = script_text.index(f"@{event.x},{event.y}")
            line_num = int(index.split('.')[0])
            
            if 1 <= line_num <= len(test_images):
                # 显示对应的测试图片
                img = test_images[line_num - 1]
                
                # 转换为PIL图像
                rgb_img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                pil_img = Image.fromarray(rgb_img)
                
                # 调整大小
                pil_img.thumbnail((220, 150), Image.Resampling.LANCZOS)
                
                # 转换为Tkinter格式
                tk_img = ImageTk.PhotoImage(pil_img)
                preview_label.config(image=tk_img, text="")
                preview_label.image = tk_img  # 保持引用
                
                # 更新信息
                step_info = f"步骤 {line_num}: {['登录按钮', '用户名输入框', '密码输入框', '确认按钮'][line_num-1]}"
                root.title(f"测试模板图片预览布局 - {step_info}")
            else:
                preview_label.config(image="", text="点击步骤查看模板图片")
                root.title("测试模板图片预览布局")
                
        except Exception as e:
            print(f"点击处理失败: {e}")
    
    # 绑定点击事件
    script_text.bind("<Button-1>", on_text_click)
    
    # 编辑按钮区域
    edit_frame = ttk.Frame(script_frame)
    edit_frame.pack(fill="x", pady=5)
    
    ttk.Button(edit_frame, text="✏️ 编辑选中步骤").pack(side=tk.LEFT, padx=3)
    
    # 说明
    info_label = ttk.Label(root, text="点击左侧的步骤文本，右侧会显示对应的模板图片预览")
    info_label.pack(pady=5)
    
    root.mainloop()

def test_batch_dialog():
    """测试批量设置对话框"""
    root = tk.Tk()
    root.title("测试批量设置间隔")
    root.geometry("300x200")
    
    def show_dialog():
        dialog = tk.Toplevel(root)
        dialog.title("批量设置步骤间隔")
        dialog.geometry("400x200")
        dialog.transient(root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (200 // 2)
        dialog.geometry(f"400x200+{x}+{y}")
        
        # 主框架
        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill="both", expand=True)
        
        # 说明文本
        info_label = ttk.Label(main_frame, 
                              text="当前共有 4 个脚本步骤\n请输入新的执行间隔时间（秒）：",
                              font=('Segoe UI', 10))
        info_label.pack(pady=(0, 15))
        
        # 输入框
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill="x", pady=(0, 15))
        
        ttk.Label(input_frame, text="间隔时间：").pack(side=tk.LEFT)
        interval_var = tk.StringVar(value="1.0")
        interval_entry = ttk.Entry(input_frame, textvariable=interval_var, width=10)
        interval_entry.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(input_frame, text="秒").pack(side=tk.LEFT, padx=(5, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(15, 0))
        
        def apply_setting():
            try:
                new_interval = float(interval_var.get().strip())
                dialog.destroy()
                messagebox.showinfo("批量设置完成", 
                                  f"已将所有 4 个步骤的间隔时间设置为 {new_interval} 秒")
            except ValueError:
                messagebox.showerror("输入错误", "请输入有效的数字")
        
        def cancel_setting():
            dialog.destroy()
        
        # 按钮
        ttk.Button(button_frame, text="应用", command=apply_setting).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=cancel_setting).pack(side=tk.RIGHT)
        
        # 焦点设置
        interval_entry.focus_set()
        interval_entry.select_range(0, tk.END)
    
    # 测试按钮
    test_button = ttk.Button(root, text="测试批量设置间隔对话框", command=show_dialog)
    test_button.pack(expand=True)
    
    root.mainloop()

if __name__ == "__main__":
    print("选择测试功能：")
    print("1. 测试模板图片预览布局")
    print("2. 测试批量设置间隔对话框")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        test_preview_layout()
    elif choice == "2":
        test_batch_dialog()
    else:
        print("无效选择，退出测试")

import asyncio
from concurrent.futures import ThreadPoolExecutor
import socket
import lz4.frame
import win32gui
import win32ui
import win32con
from PIL import Image
import io

from WindowFinder import WindowFinder


# 截取特定窗口的截图
def take_screenshot(hwnd):
    wDC = win32gui.GetWindowDC(hwnd)
    dcObj = win32ui.CreateDCFromHandle(wDC)
    cDC = dcObj.CreateCompatibleDC()
    rect = win32gui.GetWindowRect(hwnd)
    width = rect[2] - rect[0]
    height = rect[3] - rect[1]
    bmp = win32ui.CreateBitmap()
    bmp.CreateCompatibleBitmap(dcObj, width, height)
    cDC.SelectObject(bmp)
    cDC.BitBlt((0, 0), (width, height), dcObj, (0, 0), win32con.SRCCOPY)
    bmpinfo = bmp.GetInfo()
    bmpstr = bmp.GetBitmapBits(True)
    img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), bmpstr, 'raw', 'BGRX', 0, 1)

    # 释放资源
    dcObj.DeleteDC()
    cDC.DeleteDC()
    win32gui.ReleaseDC(hwnd, wDC)
    win32gui.DeleteObject(bmp.GetHandle())

    return img


async def send_data_packet(sock, host, port, packet):
    # 发送数据包，这里不需要executor，因为我们不在这一步处理阻塞的操作
    sock.sendto(packet, (host, port))


async def send_large_data_async(host, port, data, chunk_size=1472):
    with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
        total_chunks = len(data) // chunk_size + (1 if len(data) % chunk_size else 0)
        for i in range(total_chunks):
            chunk = data[i * chunk_size:(i + 1) * chunk_size]
            header = f"{i + 1}/{total_chunks},".encode()
            packet = header + chunk
            await send_data_packet(sock, host, port, packet)


async def process_images_async(hwnd, host, port):
    loop = asyncio.get_running_loop()
    image = await loop.run_in_executor(None, take_screenshot, hwnd)
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='JPEG', quality=85)
    compressed_data = lz4.frame.compress(img_byte_arr.getvalue())
    await send_large_data_async(host, port, compressed_data)


async def main(hwnd, host, port):
    while True:
        await process_images_async(hwnd, host, port)
        await asyncio.sleep(0.2)  # 控制截图发送频率


if __name__ == '__main__':
    child_hwnd = win32gui.GetDesktopWindow()  # 示例：获取桌面窗口的句柄
    asyncio.run(main(child_hwnd, '**************', 12345))  # 替换为你的服务器IP和端口


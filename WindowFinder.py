import win32gui

class WindowFinder:
    def __init__(self, class_name=None, window_name=None):
        self.class_name = class_name
        self.window_name = window_name
        self.found_hwnd = None

    def callback_by_class_name(self, hwnd, extra):
        if self.class_name is not None and win32gui.GetClassName(hwnd) == self.class_name:
            self.found_hwnd = hwnd
            return False  # 告诉Windows停止枚举更多的窗口
        return True

    def callback_by_window_name(self, hwnd, extra):
        if self.window_name is not None and win32gui.GetWindowText(hwnd) == self.window_name:
            self.found_hwnd = hwnd
            return False  # 告诉Windows停止枚举更多的窗口
        return True

    def find_child_window_by_class_name(self, parent_handle):
        win32gui.EnumChildWindows(parent_handle, self.callback_by_class_name, None)
        return self.found_hwnd

    def find_child_window_by_window_name(self, parent_handle):
        win32gui.EnumChildWindows(parent_handle, self.callback_by_window_name, None)
        return self.found_hwnd

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除功能的脚本
"""

import os
import json

def check_autosave_method():
    """检查自动保存方法是否存在"""
    print("=== 检查删除功能修复 ===")
    
    # 读取automation_ui.py文件
    with open('automation_ui.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含正确的方法调用
    if 'self._autosave_save()' in content:
        print("✅ 修复成功：找到正确的 _autosave_save() 方法调用")
    else:
        print("❌ 修复失败：未找到 _autosave_save() 方法调用")
    
    # 检查是否还有错误的方法调用
    if 'self._autosave()' in content and 'self._autosave_save()' not in content.replace('self._autosave()', ''):
        print("❌ 仍存在错误的 _autosave() 方法调用")
    else:
        print("✅ 没有发现错误的 _autosave() 方法调用")
    
    # 检查删除方法是否存在
    if 'def _delete_script_step(self, step_index):' in content:
        print("✅ 删除步骤方法存在")
    else:
        print("❌ 删除步骤方法不存在")
    
    # 检查右键菜单方法是否存在
    if 'def on_script_right_click(self, event):' in content:
        print("✅ 右键菜单方法存在")
    else:
        print("❌ 右键菜单方法不存在")
    
    print()
    print("=== 测试建议 ===")
    print("1. 启动 automation_ui.py")
    print("2. 切换到'脚本工作流'标签")
    print("3. 添加几个动作到工作流")
    print("4. 右键点击任意步骤行")
    print("5. 选择'删除步骤 X'")
    print("6. 确认删除")
    print("7. 检查步骤是否被删除且编号重新排列")
    print("8. 检查是否有错误信息")
    print()
    
    # 检查当前是否有自动保存文件
    if os.path.exists('autosave_workflow.json'):
        print("📁 发现自动保存文件：autosave_workflow.json")
        try:
            with open('autosave_workflow.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                steps_count = len(data.get('steps', []))
                print(f"   当前包含 {steps_count} 个步骤")
        except Exception as e:
            print(f"   读取文件失败：{e}")
    else:
        print("📁 未发现自动保存文件")

if __name__ == "__main__":
    check_autosave_method()

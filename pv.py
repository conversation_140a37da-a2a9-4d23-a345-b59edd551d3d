import numpy as np
import cv2
from PIL import ImageGrab
import win32api

width = win32api.GetSystemMetrics(0)
height = win32api.GetSystemMetrics(1)
print('width:', width, 'height:', height)
# height=600
while True:
    img = ImageGrab.grab(bbox=(0, 0, width, height))  # bbox specifies specific region (bbox= x,y,width,height)
    img_np = np.array(img)
    height, width, _ = img_np.shape
    half_width = width // 2
    left_frame = img_np[:, :half_width, :]
    right_frame = img_np[:, half_width:, :]
    left_frame = cv2.cvtColor(left_frame, cv2.COLOR_BGR2RGB)
    right_frame = cv2.cvtColor(right_frame, cv2.COLOR_BGR2RGB)
    cv2.imshow("Left Screen", left_frame)
    cv2.imshow("Right Screen", right_frame)
    key = cv2.waitKey(1)
    if key == 27:
        break
cv2.destroyAllWindows()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确搜索修复
分析为什么没有找到(242, 212)位置，并提供更精确的搜索
"""

from image_color_script import ImageColorScript
import cv2
import numpy as np

def analyze_missing_position():
    """分析为什么没有找到正确位置"""
    print("=== 分析缺失位置 ===")
    print("检查为什么没有找到(242, 212)位置")
    print()
    
    script = ImageColorScript()
    screen_img = script.capture_screen()
    template_path = "template.bmp"
    
    # 加载模板
    template = cv2.imread(template_path)
    if template is None:
        print("❌ 无法加载模板")
        return
    
    h, w = template.shape[:2]
    correct_x, correct_y = 242, 212
    
    print(f"🔍 检查正确位置({correct_x}, {correct_y}):")
    
    # 1. 检查边界
    if (correct_x + w > screen_img.shape[1] or 
        correct_y + h > screen_img.shape[0]):
        print(f"❌ 位置超出屏幕边界")
        print(f"   屏幕尺寸: {screen_img.shape[1]} x {screen_img.shape[0]}")
        print(f"   模板尺寸: {w} x {h}")
        print(f"   需要空间: {correct_x + w} x {correct_y + h}")
        return
    
    # 2. 提取正确位置的区域
    correct_region = screen_img[correct_y:correct_y+h, correct_x:correct_x+w]
    
    # 3. 计算各种相似度
    template_sim = script._compare_template(correct_region, template)
    mse_sim = script._compare_mse(correct_region, template)
    
    print(f"✅ 正确位置的相似度:")
    print(f"   Template相似度: {template_sim:.3f}")
    print(f"   MSE相似度: {mse_sim:.3f}")
    
    # 4. 检查是否满足当前阈值
    current_template_threshold = 0.4
    current_mse_threshold = 0.8
    
    print(f"\n🎯 阈值检查:")
    print(f"   当前Template阈值: {current_template_threshold}")
    print(f"   当前MSE阈值: {current_mse_threshold}")
    
    if template_sim >= current_template_threshold:
        print(f"   ✅ Template阈值通过")
    else:
        print(f"   ❌ Template阈值未通过，需要降低到 {template_sim - 0.01:.2f}")
    
    if mse_sim >= current_mse_threshold:
        print(f"   ✅ MSE阈值通过")
    else:
        print(f"   ❌ MSE阈值未通过，需要降低到 {mse_sim - 0.01:.2f}")
    
    # 5. 保存正确位置的对比图像
    comparison_img = np.hstack([template, correct_region])
    cv2.imwrite("correct_position_comparison.png", comparison_img)
    print(f"\n💾 对比图像已保存: correct_position_comparison.png")
    
    return template_sim, mse_sim

def enhanced_search_with_lower_thresholds():
    """使用更低阈值的增强搜索"""
    print(f"\n=== 增强搜索（更低阈值）===")
    
    script = ImageColorScript()
    screen_img = script.capture_screen()
    template_path = "template.bmp"
    
    # 获取正确位置的实际相似度
    template_sim, mse_sim = analyze_missing_position()
    
    if template_sim is None or mse_sim is None:
        print("❌ 无法获取正确位置的相似度")
        return
    
    # 使用略低于实际相似度的阈值
    new_template_threshold = max(0.1, template_sim - 0.05)
    new_mse_threshold = max(0.5, mse_sim - 0.05)
    
    print(f"\n🔧 使用调整后的阈值:")
    print(f"   Template阈值: {new_template_threshold:.3f}")
    print(f"   MSE阈值: {new_mse_threshold:.3f}")
    
    # 搜索所有候选
    all_matches = script.smart_find_image(
        screen_img,
        template_path,
        mode='all',
        template_threshold=new_template_threshold,
        mse_threshold=new_mse_threshold
    )
    
    if all_matches:
        print(f"\n🎯 找到 {len(all_matches)} 个候选:")
        
        correct_found = False
        for i, (x, y, sim) in enumerate(all_matches):
            distance = abs(x - 242) + abs(y - 212)
            status = "🎯 正确位置!" if distance < 5 else "✅ 很接近" if distance < 20 else "⚠️ 接近" if distance < 50 else "❌ 较远"
            print(f"   候选{i+1}: ({x}, {y}) MSE={sim:.3f} 距离={distance}px {status}")
            
            if distance < 5:
                correct_found = True
        
        if correct_found:
            print(f"\n🎉 成功找到正确位置!")
        else:
            print(f"\n⚠️ 仍未找到精确位置，最接近的候选:")
            best_match = min(all_matches, key=lambda m: abs(m[0] - 242) + abs(m[1] - 212))
            x, y, sim = best_match
            distance = abs(x - 242) + abs(y - 212)
            print(f"   位置: ({x}, {y}) MSE={sim:.3f} 距离={distance}px")
        
        # 保存结果
        script.save_marked_image(
            screen_img,
            "enhanced_search_result.png",
            all_matches,
            template_path,
            color=(255, 0, 255),  # 紫色
            show_info=True
        )
        print(f"\n💾 增强搜索结果已保存: enhanced_search_result.png")
    
    else:
        print(f"\n❌ 即使使用更低阈值也未找到匹配")

def grid_search_around_correct_position():
    """在正确位置周围进行网格搜索"""
    print(f"\n=== 网格搜索验证 ===")
    print("在(242, 212)周围搜索最佳匹配")
    
    script = ImageColorScript()
    screen_img = script.capture_screen()
    template_path = "template.bmp"
    
    template = cv2.imread(template_path)
    if template is None:
        print("❌ 无法加载模板")
        return
    
    h, w = template.shape[:2]
    center_x, center_y = 242, 212
    search_radius = 20  # 在20像素范围内搜索
    
    print(f"🔍 在({center_x}, {center_y})周围{search_radius}像素范围内搜索...")
    
    best_position = None
    best_mse = 0
    results = []
    
    for dx in range(-search_radius, search_radius + 1, 2):  # 每2像素搜索一次
        for dy in range(-search_radius, search_radius + 1, 2):
            x = center_x + dx
            y = center_y + dy
            
            # 检查边界
            if (x < 0 or y < 0 or 
                x + w > screen_img.shape[1] or 
                y + h > screen_img.shape[0]):
                continue
            
            # 提取区域并计算MSE相似度
            region = screen_img[y:y+h, x:x+w]
            mse_sim = script._compare_mse(region, template)
            
            results.append((x, y, mse_sim))
            
            if mse_sim > best_mse:
                best_mse = mse_sim
                best_position = (x, y)
    
    # 排序结果
    results.sort(key=lambda r: r[2], reverse=True)
    
    print(f"\n🏆 网格搜索结果（前10个）:")
    for i, (x, y, sim) in enumerate(results[:10]):
        distance = abs(x - center_x) + abs(y - center_y)
        status = "🎯 中心" if distance == 0 else f"偏移{distance}px"
        print(f"   位置{i+1}: ({x}, {y}) MSE={sim:.3f} {status}")
    
    if best_position:
        print(f"\n🎯 最佳位置: {best_position} MSE={best_mse:.3f}")
        
        # 标记最佳位置
        marked_img = screen_img.copy()
        x, y = best_position
        cv2.rectangle(marked_img, (x, y), (x + w, y + h), (0, 255, 255), 3)  # 黄色框
        cv2.putText(marked_img, f"Best: ({x},{y}) MSE={best_mse:.3f}", 
                   (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 也标记原始目标位置
        cv2.rectangle(marked_img, (center_x, center_y), (center_x + w, center_y + h), (0, 0, 255), 2)  # 红色框
        cv2.putText(marked_img, f"Target: ({center_x},{center_y})", 
                   (center_x, center_y-30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        cv2.imwrite("grid_search_result.png", marked_img)
        print(f"💾 网格搜索结果已保存: grid_search_result.png")

def create_optimized_parameters():
    """创建优化的参数建议"""
    print(f"\n=== 优化参数建议 ===")
    
    # 基于分析结果创建参数建议
    template_sim, mse_sim = analyze_missing_position()
    
    if template_sim is not None and mse_sim is not None:
        recommended_template_threshold = max(0.1, template_sim - 0.1)
        recommended_mse_threshold = max(0.5, mse_sim - 0.1)
        
        print(f"📊 基于正确位置的相似度分析:")
        print(f"   正确位置Template相似度: {template_sim:.3f}")
        print(f"   正确位置MSE相似度: {mse_sim:.3f}")
        print(f"\n🎯 推荐参数:")
        print(f"   template_threshold = {recommended_template_threshold:.3f}")
        print(f"   mse_threshold = {recommended_mse_threshold:.3f}")
        
        print(f"\n💡 使用代码:")
        print(f"result = script.smart_find_image(")
        print(f"    screen_img, 'template.bmp',")
        print(f"    mode='best',")
        print(f"    template_threshold={recommended_template_threshold:.3f},")
        print(f"    mse_threshold={recommended_mse_threshold:.3f}")
        print(f")")

def main():
    """主函数"""
    print("=" * 60)
    print("精确搜索问题分析与修复")
    print("=" * 60)
    print("目标: 找到为什么没有检测到(242, 212)位置")
    print("=" * 60)
    
    # 分析正确位置的相似度
    analyze_missing_position()
    
    # 使用调整后的阈值搜索
    enhanced_search_with_lower_thresholds()
    
    # 网格搜索验证
    grid_search_around_correct_position()
    
    # 创建优化参数建议
    create_optimized_parameters()
    
    print(f"\n" + "=" * 60)
    print("🎯 总结:")
    print("1. 检查了正确位置的实际相似度")
    print("2. 使用调整后的阈值重新搜索")
    print("3. 进行了网格搜索验证")
    print("4. 提供了优化的参数建议")
    print("5. 生成了多个调试图像供分析")

if __name__ == "__main__":
    main()

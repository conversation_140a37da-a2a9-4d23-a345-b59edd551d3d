#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相似度搜索功能示例
展示如何使用不同的相似度方法查找图像中的匹配区域
"""

from image_color_script import ImageColorScript

def demo_similarity_search():
    """演示相似度搜索功能"""
    print("=== 相似度搜索功能演示 ===")
    
    script = ImageColorScript()
    
    try:
        # 1. 使用模板匹配方法（最快）
        print("1. 模板匹配方法搜索...")
        results_template = script.find_similar_regions(
            "test_full_screen.png", 
            "test_region_screen.png",
            method='template',
            threshold=0.8,
            max_results=5
        )
        
        print(f"   找到 {len(results_template)} 个匹配区域:")
        for i, (x, y, similarity) in enumerate(results_template):
            print(f"   {i+1}. 位置: ({x}, {y}), 相似度: {similarity:.3f}")
        
        # 2. 使用直方图方法
        print("\n2. 直方图方法搜索...")
        results_hist = script.find_similar_regions(
            "test_full_screen.png", 
            "test_region_screen.png",
            method='histogram',
            threshold=0.7,
            max_results=3
        )
        
        print(f"   找到 {len(results_hist)} 个匹配区域:")
        for i, (x, y, similarity) in enumerate(results_hist):
            print(f"   {i+1}. 位置: ({x}, {y}), 相似度: {similarity:.3f}")
        
        # 3. 使用MSE方法
        print("\n3. MSE方法搜索...")
        results_mse = script.find_similar_regions(
            "test_full_screen.png", 
            "test_region_screen.png",
            method='mse',
            threshold=0.9,
            max_results=3
        )
        
        print(f"   找到 {len(results_mse)} 个匹配区域:")
        for i, (x, y, similarity) in enumerate(results_mse):
            print(f"   {i+1}. 位置: ({x}, {y}), 相似度: {similarity:.3f}")
        
        # 4. 查找最佳匹配
        print("\n4. 查找最佳匹配...")
        best_match = script.find_best_match_region(
            "test_full_screen.png", 
            "test_region_screen.png",
            method='template'
        )
        
        if best_match:
            x, y, similarity = best_match
            print(f"   最佳匹配: 位置({x}, {y}), 相似度: {similarity:.3f}")
        else:
            print("   未找到匹配")
        
        # 5. 查找所有超过阈值的匹配
        print("\n5. 查找所有高相似度匹配...")
        all_matches = script.find_all_matches_above_threshold(
            "test_full_screen.png", 
            "test_region_screen.png",
            method='template',
            threshold=0.95,
            max_results=10
        )
        
        print(f"   找到 {len(all_matches)} 个高相似度匹配:")
        for i, (x, y, similarity) in enumerate(all_matches):
            print(f"   {i+1}. 位置: ({x}, {y}), 相似度: {similarity:.3f}")
        
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        print("请确保 test_full_screen.png 和 test_region_screen.png 文件存在")
    except Exception as e:
        print(f"搜索过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def demo_different_thresholds():
    """演示不同阈值的效果"""
    print("\n=== 不同阈值效果演示 ===")
    
    script = ImageColorScript()
    
    thresholds = [0.5, 0.7, 0.8, 0.9, 0.95]
    
    try:
        for threshold in thresholds:
            print(f"\n阈值 {threshold}:")
            results = script.find_similar_regions(
                "test_full_screen.png", 
                "test_region_screen.png",
                method='template',
                threshold=threshold,
                max_results=5
            )
            
            print(f"   找到 {len(results)} 个匹配区域")
            for i, (x, y, similarity) in enumerate(results[:3]):  # 只显示前3个
                print(f"   {i+1}. 位置: ({x}, {y}), 相似度: {similarity:.3f}")
    
    except Exception as e:
        print(f"演示过程中出现错误: {e}")

def demo_method_comparison():
    """演示不同方法的比较"""
    print("\n=== 不同方法比较 ===")
    
    script = ImageColorScript()
    methods = ['template', 'histogram', 'mse', 'ssim']
    
    try:
        for method in methods:
            print(f"\n{method.upper()} 方法:")
            
            # 设置不同方法的合适阈值
            if method == 'template':
                threshold = 0.8
            elif method == 'histogram':
                threshold = 0.6
            elif method == 'mse':
                threshold = 0.9
            else:  # ssim
                threshold = 0.7
            
            results = script.find_similar_regions(
                "test_full_screen.png", 
                "test_region_screen.png",
                method=method,
                threshold=threshold,
                max_results=3
            )
            
            print(f"   阈值: {threshold}, 找到 {len(results)} 个匹配")
            for i, (x, y, similarity) in enumerate(results):
                print(f"   {i+1}. 位置: ({x}, {y}), 相似度: {similarity:.3f}")
    
    except Exception as e:
        print(f"比较过程中出现错误: {e}")

def practical_usage_examples():
    """实际使用示例"""
    print("\n=== 实际使用示例 ===")
    
    script = ImageColorScript()
    
    print("示例1: 在游戏截图中查找按钮")
    print("# 查找所有相似的按钮")
    print("buttons = script.find_similar_regions('game_screen.png', 'button_template.png', method='template', threshold=0.8)")
    print("for x, y, similarity in buttons:")
    print("    print(f'按钮位置: ({x}, {y}), 匹配度: {similarity:.2f}')")
    print("    # 可以点击找到的按钮")
    print("    # script.click(x + button_width//2, y + button_height//2)")
    
    print("\n示例2: 查找最相似的图标")
    print("# 查找最佳匹配的图标")
    print("best_icon = script.find_best_match_region('desktop.png', 'app_icon.png', method='histogram')")
    print("if best_icon:")
    print("    x, y, similarity = best_icon")
    print("    print(f'最佳匹配图标位置: ({x}, {y}), 相似度: {similarity:.2f}')")
    
    print("\n示例3: 查找所有高质量匹配")
    print("# 查找所有高质量匹配的文字")
    print("text_matches = script.find_all_matches_above_threshold(")
    print("    'document.png', 'text_pattern.png', method='ssim', threshold=0.9)")
    print("print(f'找到 {len(text_matches)} 个高质量文字匹配')")

def main():
    """主函数"""
    print("=" * 60)
    print("图像相似度搜索功能演示")
    print("=" * 60)
    print("新增功能:")
    print("• find_similar_regions() - 查找相似区域")
    print("• find_best_match_region() - 查找最佳匹配")
    print("• find_all_matches_above_threshold() - 查找所有高质量匹配")
    print("• 支持多种相似度算法: template, histogram, mse, ssim")
    print("• 可设置相似度阈值")
    print("• 自动去重和排序")
    print("=" * 60)
    
    demo_similarity_search()
    demo_different_thresholds()
    demo_method_comparison()
    practical_usage_examples()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("\n💡 使用建议:")
    print("• template方法最快，适合精确匹配")
    print("• histogram方法适合颜色相似的匹配")
    print("• mse方法适合像素级精确比较")
    print("• ssim方法适合结构相似性比较")
    print("• 根据具体需求选择合适的阈值")

if __name__ == "__main__":
    main()

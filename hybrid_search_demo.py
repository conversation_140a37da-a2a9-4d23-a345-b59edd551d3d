#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合搜索策略演示
Template快速定位 + MSE精确验证
"""

import time
from image_color_script import ImageColorScript

def demo_hybrid_search():
    """演示混合搜索策略"""
    print("=== 混合搜索策略演示 ===")
    print("策略: Template快速定位 → MSE精确验证")
    print("优势: 速度快 + 精度高")
    print()
    
    script = ImageColorScript()
    
    # 测试图像
    large_img = "test_full_screen.png"
    template = "test_region_screen.png"
    
    try:
        # 1. 混合搜索 - 找最佳匹配
        print("1. 🎯 混合搜索 - 最佳匹配")
        start_time = time.time()
        
        best_match = script.find_best_match_hybrid(
            large_img, template,
            template_threshold=0.7,  # template阈值较低，快速筛选
            mse_threshold=0.9,       # MSE阈值较高，精确验证
            max_template_results=20  # 最多验证20个候选
        )
        
        hybrid_time = time.time() - start_time
        
        if best_match:
            x, y, mse_similarity = best_match
            print(f"   ✅ 找到最佳匹配:")
            print(f"      位置: ({x}, {y})")
            print(f"      MSE相似度: {mse_similarity:.3f}")
            print(f"      耗时: {hybrid_time:.3f}秒")
        else:
            print(f"   ❌ 未找到匹配")
            print(f"      耗时: {hybrid_time:.3f}秒")
        
        # 2. 混合搜索 - 找所有匹配
        print(f"\n2. 🔍 混合搜索 - 所有匹配")
        start_time = time.time()
        
        all_matches = script.find_all_matches_hybrid(
            large_img, template,
            template_threshold=0.6,   # 稍微降低template阈值
            mse_threshold=0.85,       # 稍微降低MSE阈值
            max_template_results=30,  # 更多template候选
            max_final_results=5       # 最终返回5个
        )
        
        all_matches_time = time.time() - start_time
        
        print(f"   找到 {len(all_matches)} 个验证通过的匹配")
        print(f"   耗时: {all_matches_time:.3f}秒")
        
        for i, (x, y, similarity) in enumerate(all_matches):
            print(f"   匹配{i+1}: 位置({x}, {y}), MSE相似度: {similarity:.3f}")
        
        # 3. 智能搜索
        print(f"\n3. 🧠 智能搜索")
        start_time = time.time()
        
        smart_result = script.smart_find_image(
            large_img, template,
            mode='best',              # 'best' 或 'all'
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        smart_time = time.time() - start_time
        
        if smart_result:
            x, y, similarity = smart_result
            print(f"   ✅ 智能搜索结果:")
            print(f"      位置: ({x}, {y})")
            print(f"      相似度: {similarity:.3f}")
            print(f"      耗时: {smart_time:.3f}秒")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def performance_comparison():
    """性能对比"""
    print("\n=== 性能对比测试 ===")
    
    script = ImageColorScript()
    large_img = "test_full_screen.png"
    template = "test_region_screen.png"
    
    try:
        # 1. 纯Template方法
        print("1. 纯Template方法:")
        start_time = time.time()
        
        template_results = script.find_similar_regions(
            large_img, template,
            method='template',
            threshold=0.8,
            max_results=1
        )
        
        template_time = time.time() - start_time
        print(f"   耗时: {template_time:.3f}秒")
        print(f"   结果数: {len(template_results)}")
        if template_results:
            print(f"   相似度: {template_results[0][2] if len(template_results[0]) > 2 else 'N/A'}")
        
        # 2. 纯MSE方法（优化后）
        print(f"\n2. 纯MSE方法（优化后）:")
        start_time = time.time()
        
        mse_results = script.find_similar_regions_fast(
            large_img, template,
            method='mse',
            threshold=0.9,
            max_results=1,
            speed_mode='balanced'
        )
        
        mse_time = time.time() - start_time
        print(f"   耗时: {mse_time:.3f}秒")
        print(f"   结果数: {len(mse_results)}")
        if mse_results:
            print(f"   相似度: {mse_results[0][2]:.3f}")
        
        # 3. 混合方法
        print(f"\n3. 混合方法（Template + MSE）:")
        start_time = time.time()
        
        hybrid_result = script.find_best_match_hybrid(
            large_img, template,
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        hybrid_time = time.time() - start_time
        print(f"   耗时: {hybrid_time:.3f}秒")
        print(f"   结果: {'找到' if hybrid_result else '未找到'}")
        if hybrid_result:
            print(f"   MSE相似度: {hybrid_result[2]:.3f}")
        
        # 性能总结
        print(f"\n📊 性能总结:")
        print(f"   Template方法: {template_time:.3f}秒 (最快，但精度一般)")
        print(f"   MSE方法: {mse_time:.3f}秒 (较慢，但精度高)")
        print(f"   混合方法: {hybrid_time:.3f}秒 (平衡速度和精度)")
        
        if template_time > 0 and hybrid_time > 0:
            ratio = hybrid_time / template_time
            print(f"   混合/Template比: {ratio:.1f}x")
        
    except Exception as e:
        print(f"性能对比出现错误: {e}")

def practical_examples():
    """实际应用示例"""
    print("\n=== 实际应用示例 ===")
    
    examples = [
        {
            "name": "游戏脚本 - 精确找BOSS",
            "description": "需要高精度识别BOSS，避免误击小怪",
            "code": """
# 游戏中精确找BOSS
boss_position = script.find_best_match_hybrid(
    'game_screen.png', 'boss.png',
    template_threshold=0.6,  # template快速筛选
    mse_threshold=0.95,      # MSE高精度验证
    max_template_results=10
)

if boss_position:
    x, y, accuracy = boss_position
    print(f'发现BOSS: ({x}, {y}), 精度: {accuracy:.3f}')
    if accuracy > 0.95:  # 高精度才攻击
        script.click(x + 50, y + 50)
            """,
        },
        {
            "name": "UI自动化 - 找特定按钮",
            "description": "在复杂界面中精确找到特定按钮",
            "code": """
# UI中精确找按钮
button = script.find_best_match_hybrid(
    'app_interface.png', 'submit_btn.png',
    template_threshold=0.7,
    mse_threshold=0.9
)

if button:
    x, y, similarity = button
    print(f'找到按钮: ({x}, {y}), 相似度: {similarity:.3f}')
    script.click(x + 25, y + 12)  # 点击按钮中心
else:
    print('按钮未找到，可能界面已变化')
            """,
        },
        {
            "name": "批量处理 - 智能搜索",
            "description": "批量处理多个图像，自动选择最佳策略",
            "code": """
# 批量智能搜索
images = ['screen1.png', 'screen2.png', 'screen3.png']
target = 'target_icon.png'

for img in images:
    # 智能搜索，自动优化
    result = script.smart_find_image(
        img, target,
        mode='best',
        template_threshold=0.7,
        mse_threshold=0.9
    )
    
    if result:
        x, y, similarity = result
        print(f'{img}: 找到目标 ({x}, {y}), 精度: {similarity:.3f}')
    else:
        print(f'{img}: 未找到目标')
            """,
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['name']}")
        print(f"   场景: {example['description']}")
        print(f"   代码:")
        for line in example['code'].strip().split('\n'):
            print(f"     {line}")

def usage_recommendations():
    """使用建议"""
    print("\n=== 使用建议 ===")
    
    recommendations = [
        "🎯 混合搜索策略优势:",
        "",
        "1. 速度优势:",
        "   • Template方法快速筛选候选",
        "   • 只对少量候选进行MSE验证",
        "   • 避免全图MSE扫描",
        "",
        "2. 精度优势:",
        "   • Template粗筛 + MSE精验",
        "   • 最终结果具有MSE级别的精度",
        "   • 避免Template方法的误匹配",
        "",
        "3. 参数调优建议:",
        "   • template_threshold: 0.6-0.8 (较宽松)",
        "   • mse_threshold: 0.85-0.95 (较严格)",
        "   • max_template_results: 10-30",
        "",
        "4. 适用场景:",
        "   • 需要高精度的图像匹配",
        "   • 复杂背景下的目标识别",
        "   • 游戏脚本中的精确定位",
        "   • UI自动化中的控件识别",
        "",
        "5. 最佳实践:",
        "   • 优先使用 find_best_match_hybrid()",
        "   • 需要多个结果时使用 find_all_matches_hybrid()",
        "   • 简单场景可以直接用 smart_find_image()"
    ]
    
    for rec in recommendations:
        print(f"  {rec}")

def main():
    """主函数"""
    print("=" * 60)
    print("混合搜索策略 - Template + MSE")
    print("=" * 60)
    print("💡 您的想法实现:")
    print("1. Template方法快速找到所有可能的匹配")
    print("2. MSE方法对候选进行精确验证")
    print("3. 返回MSE相似度最高的结果")
    print("4. 既快速又精确！")
    print("=" * 60)
    
    demo_hybrid_search()
    performance_comparison()
    practical_examples()
    usage_recommendations()
    
    print("\n" + "=" * 60)
    print("🎉 混合搜索策略实现完成！")
    print("\n✅ 核心优势:")
    print("• 速度: Template快速筛选，避免全图MSE扫描")
    print("• 精度: MSE精确验证，确保结果准确")
    print("• 智能: 自动选择最佳候选")
    print("• 灵活: 支持单个/多个结果返回")
    print("\n🚀 推荐用法:")
    print("result = script.find_best_match_hybrid(")
    print("    'large.png', 'template.png',")
    print("    template_threshold=0.7,")
    print("    mse_threshold=0.9")
    print(")")

if __name__ == "__main__":
    main()

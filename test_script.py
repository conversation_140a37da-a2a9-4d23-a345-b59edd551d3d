#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图色脚本功能测试
简单的测试脚本，验证各项功能是否正常工作
"""

from zhaoTuZhaoSe import ImageColorScript
import time
import os

def test_screenshot_functions():
    """测试截图功能"""
    print("=== 测试截图功能 ===")
    
    script = ImageColorScript()
    
    # 测试全屏截图
    print("1. 测试全屏截图...")
    if script.save_screenshot("test_full_screen.png"):
        print("   ✓ 全屏截图成功")
        if os.path.exists("test_full_screen.png"):
            print("   ✓ 截图文件已保存")
        else:
            print("   ✗ 截图文件未找到")
    else:
        print("   ✗ 全屏截图失败")
    
    # 测试区域截图
    print("2. 测试区域截图...")
    region = (0, 0, 300, 200)  # 左上角300x200区域
    if script.save_screenshot("test_region_screen.png", region=region):
        print(f"   ✓ 区域截图成功 {region}")
        if os.path.exists("test_region_screen.png"):
            print("   ✓ 区域截图文件已保存")
        else:
            print("   ✗ 区域截图文件未找到")
    else:
        print("   ✗ 区域截图失败")
    
    print()

def test_color_functions():
    """测试颜色相关功能"""
    print("=== 测试颜色功能 ===")
    
    script = ImageColorScript()
    
    # 测试获取像素颜色
    print("1. 测试获取像素颜色...")
    try:
        center_x, center_y = script.screen_width // 2, script.screen_height // 2
        color = script.get_pixel_color(center_x, center_y)
        print(f"   屏幕中心点 ({center_x}, {center_y}) 颜色: {color}")
        print("   ✓ 获取像素颜色成功")
    except Exception as e:
        print(f"   ✗ 获取像素颜色失败: {e}")
    
    # 测试颜色距离计算
    print("2. 测试颜色距离计算...")
    try:
        red = (0, 0, 255)      # BGR格式的红色
        green = (0, 255, 0)    # BGR格式的绿色
        near_red = (10, 10, 255)  # 接近红色
        
        distance1 = script.color_distance(red, green)
        distance2 = script.color_distance(red, near_red)
        
        print(f"   红色与绿色距离: {distance1:.2f}")
        print(f"   红色与近似红色距离: {distance2:.2f}")
        print("   ✓ 颜色距离计算成功")
    except Exception as e:
        print(f"   ✗ 颜色距离计算失败: {e}")
    
    # 测试找色功能
    print("3. 测试找色功能...")
    try:
        # 查找白色像素（通常屏幕上会有一些白色）
        white_positions = script.find_color((255, 255, 255), max_results=5, tolerance=20)
        print(f"   找到 {len(white_positions)} 个白色像素点")
        if white_positions:
            print(f"   前3个位置: {white_positions[:3]}")
            print("   ✓ 找色功能正常")
        else:
            print("   ! 未找到白色像素（可能正常，取决于当前屏幕内容）")
    except Exception as e:
        print(f"   ✗ 找色功能失败: {e}")
    
    print()

def test_image_functions():
    """测试图像相关功能"""
    print("=== 测试图像功能 ===")

    script = ImageColorScript()

    # 测试图像比较（需要先有截图文件）
    print("1. 测试图像比较...")
    try:
        if os.path.exists("test_full_screen.png") and os.path.exists("test_region_screen.png"):
            print("   测试不同比较方法:")

            # 测试直方图比较
            similarity_hist = script.compare_images("test_full_screen.png", "test_region_screen.png", method='histogram')
            print(f"   - 直方图比较相似度: {similarity_hist:.3f}")

            # 测试MSE比较
            similarity_mse = script.compare_images("test_full_screen.png", "test_region_screen.png", method='mse')
            print(f"   - MSE比较相似度: {similarity_mse:.3f}")

            # 测试SSIM比较
            similarity_ssim = script.compare_images("test_full_screen.png", "test_region_screen.png", method='ssim')
            print(f"   - SSIM比较相似度: {similarity_ssim:.3f}")

            # 测试模板匹配比较
            similarity_template = script.compare_images("test_full_screen.png", "test_region_screen.png", method='template')
            print(f"   - 模板匹配相似度: {similarity_template:.3f}")

            print("   ✓ 图像比较功能正常")

            # 测试在大图中查找小图
            print("\n2. 测试在大图中查找小图...")
            position = script.find_image_in_image("test_full_screen.png", "test_region_screen.png")
            if position:
                print(f"   ✓ 在全屏图中找到区域图，位置: {position}")
                print("   这说明区域截图确实是全屏截图的一部分")
            else:
                print("   ! 未在全屏图中找到区域图（可能阈值需要调整）")
                # 尝试降低阈值
                position_low = script.find_image_in_image("test_full_screen.png", "test_region_screen.png", threshold=0.6)
                if position_low:
                    print(f"   ✓ 降低阈值后找到位置: {position_low}")
                else:
                    print("   ! 即使降低阈值也未找到")
        else:
            print("   ! 跳过图像比较测试（缺少测试图像文件）")
    except Exception as e:
        print(f"   ✗ 图像比较失败: {e}")

    print()

def test_mouse_functions_safe():
    """安全测试鼠标功能（不实际点击）"""
    print("=== 测试鼠标功能（安全模式）===")
    
    try:
        import pyautogui
        
        # 获取当前鼠标位置
        current_pos = pyautogui.position()
        print(f"1. 当前鼠标位置: {current_pos}")
        
        # 获取屏幕尺寸
        screen_size = pyautogui.size()
        print(f"2. 屏幕尺寸: {screen_size}")
        
        print("   ✓ 鼠标功能库正常加载")
        print("   注意: 实际的鼠标操作需要谨慎使用")
        
    except Exception as e:
        print(f"   ✗ 鼠标功能测试失败: {e}")
    
    print()

def test_multi_color():
    """测试多点找色功能"""
    print("=== 测试多点找色功能 ===")
    
    script = ImageColorScript()
    
    try:
        # 定义一个简单的颜色模式
        # 这个模式可能很难在实际屏幕上找到，但可以测试功能是否正常运行
        color_pattern = [
            (0, 0, (255, 255, 255)),    # 基准点：白色
            (1, 0, (255, 255, 255)),    # 右边1像素：白色
            (0, 1, (255, 255, 255)),    # 下边1像素：白色
        ]
        
        print("1. 测试多点找色...")
        result = script.find_multi_color(color_pattern, tolerance=30)
        
        if result:
            print(f"   ✓ 找到颜色模式，位置: {result}")
        else:
            print("   ! 未找到指定颜色模式（可能正常，取决于屏幕内容）")
        
        print("   ✓ 多点找色功能运行正常")
        
    except Exception as e:
        print(f"   ✗ 多点找色功能失败: {e}")
    
    print()

def cleanup_test_files():
    """清理测试文件"""
    print("=== 清理测试文件 ===")
    
    test_files = ["test_full_screen.png", "test_region_screen.png"]
    
    for filename in test_files:
        if os.path.exists(filename):
            try:
                os.remove(filename)
                print(f"   ✓ 已删除 {filename}")
            except Exception as e:
                print(f"   ✗ 删除 {filename} 失败: {e}")
        else:
            print(f"   - {filename} 不存在")

def main():
    """主测试函数"""
    print("图色脚本功能测试开始")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_screenshot_functions()
        test_color_functions()
        test_image_functions()
        test_mouse_functions_safe()
        test_multi_color()
        
        print("=" * 50)
        print("所有测试完成！")
        
        # 询问是否清理测试文件
        response = input("\n是否删除测试生成的图片文件？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            cleanup_test_files()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("\n请确保已安装所需依赖:")
        print("pip install opencv-python numpy mss pyautogui")

if __name__ == "__main__":
    main()

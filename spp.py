import win32gui
import pyautogui


def screenshot_window(class_name, window_title):
    def enum_window_callback(hwnd, results):
        if win32gui.IsWindowVisible(hwnd) and window_title in win32gui.GetWindowText(hwnd):
            class_name_of_hwnd = win32gui.GetClassName(hwnd)
            if class_name_of_hwnd == class_name:
                results.append(hwnd)

    windows = []
    win32gui.EnumWindows(enum_window_callback, windows)

    if windows:
        # 对于找到的第一个窗口，获取它的位置和大小，然后截图
        hwnd = windows[0]
        rect = win32gui.GetWindowRect(hwnd)
        x, y, w, h = rect
        screenshot = pyautogui.screenshot(region=(x, y, w - x, h - y))
        screenshot.save("screenshot.png")
        print("截图成功，保存为screenshot.png")
    else:
        print("未找到指定窗口")


if __name__ == "__main__":
    # 用实际的类名和窗口标题替换这里的字符串
    screenshot_window("VMUIFrame", "Fx64 - VMware Workstation")

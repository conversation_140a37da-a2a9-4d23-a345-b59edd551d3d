#!/usr/bin/env python3
"""
验证新功能是否正确实现
"""

import ast
import re

def check_automation_ui():
    """检查automation_ui.py中的新功能实现"""
    print("=== 检查automation_ui.py新功能实现 ===")
    
    try:
        with open('automation_ui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查1: 模板图片预览区域
        if 'preview_image_label' in content:
            print("✓ 找到模板图片预览标签")
        else:
            print("✗ 未找到模板图片预览标签")
        
        if 'LabelFrame(main_content_frame, text="模板图片预览"' in content:
            print("✓ 找到模板图片预览区域")
        else:
            print("✗ 未找到模板图片预览区域")
        
        # 检查2: 单击事件处理
        if 'on_script_single_click' in content:
            print("✓ 找到单击事件处理方法")
        else:
            print("✗ 未找到单击事件处理方法")
        
        if '_show_template_preview' in content:
            print("✓ 找到模板预览显示方法")
        else:
            print("✗ 未找到模板预览显示方法")
        
        # 检查3: 批量设置间隔功能
        if 'batch_set_step_intervals' in content:
            print("✓ 找到批量设置间隔方法")
        else:
            print("✗ 未找到批量设置间隔方法")
        
        if 'batch_set_interval_button' in content:
            print("✓ 找到批量设置间隔按钮")
        else:
            print("✗ 未找到批量设置间隔按钮")
        
        # 检查4: 事件绑定
        if '<Button-1>' in content and 'on_script_single_click' in content:
            print("✓ 找到单击事件绑定")
        else:
            print("✗ 未找到单击事件绑定")
        
        # 检查5: 导入模块
        if 'import re' in content:
            print("✓ 找到re模块导入")
        else:
            print("✗ 未找到re模块导入")
        
        print("\n=== 功能实现检查完成 ===")
        
        # 检查语法
        try:
            ast.parse(content)
            print("✓ 语法检查通过")
        except SyntaxError as e:
            print(f"✗ 语法错误: {e}")
        
        return True
        
    except FileNotFoundError:
        print("✗ 找不到automation_ui.py文件")
        return False
    except Exception as e:
        print(f"✗ 检查过程中出错: {e}")
        return False

def check_method_implementations():
    """检查关键方法的实现"""
    print("\n=== 检查关键方法实现 ===")
    
    try:
        with open('automation_ui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_show_template_preview方法
        if 'def _show_template_preview(self, step_index):' in content:
            print("✓ _show_template_preview方法定义正确")
            
            # 检查方法内容
            if 'cv2.cvtColor' in content and 'ImageTk.PhotoImage' in content:
                print("✓ 图片转换逻辑存在")
            else:
                print("✗ 图片转换逻辑缺失")
        else:
            print("✗ _show_template_preview方法定义不正确")
        
        # 检查batch_set_step_intervals方法
        if 'def batch_set_step_intervals(self):' in content:
            print("✓ batch_set_step_intervals方法定义正确")
            
            # 检查对话框创建
            if 'tk.Toplevel' in content and '批量设置步骤间隔' in content:
                print("✓ 批量设置对话框创建逻辑存在")
            else:
                print("✗ 批量设置对话框创建逻辑缺失")
        else:
            print("✗ batch_set_step_intervals方法定义不正确")
        
        # 检查on_script_single_click方法
        if 'def on_script_single_click(self, event):' in content:
            print("✓ on_script_single_click方法定义正确")
        else:
            print("✗ on_script_single_click方法定义不正确")
        
        print("\n=== 方法实现检查完成 ===")
        
    except Exception as e:
        print(f"✗ 检查方法实现时出错: {e}")

def check_ui_layout():
    """检查UI布局修改"""
    print("\n=== 检查UI布局修改 ===")
    
    try:
        with open('automation_ui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查水平分割布局
        if 'main_content_frame' in content:
            print("✓ 找到主内容框架")
        else:
            print("✗ 未找到主内容框架")
        
        if 'left_content_frame' in content and 'right_content_frame' in content:
            print("✓ 找到左右分割布局")
        else:
            print("✗ 未找到左右分割布局")
        
        # 检查预览区域设置
        if 'width=250' in content and 'pack_propagate(False)' in content:
            print("✓ 找到预览区域尺寸设置")
        else:
            print("✗ 未找到预览区域尺寸设置")
        
        print("\n=== UI布局检查完成 ===")
        
    except Exception as e:
        print(f"✗ 检查UI布局时出错: {e}")

def main():
    """主函数"""
    print("开始验证新功能实现...")
    print("=" * 50)
    
    # 检查主要功能
    check_automation_ui()
    
    # 检查方法实现
    check_method_implementations()
    
    # 检查UI布局
    check_ui_layout()
    
    print("\n" + "=" * 50)
    print("验证完成！")
    print("\n建议测试步骤：")
    print("1. 运行automation_ui.py")
    print("2. 切换到'脚本工作流'标签")
    print("3. 添加一些动作到脚本")
    print("4. 点击步骤查看右侧模板图片预览")
    print("5. 点击'批量设置间隔'按钮测试批量设置功能")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的代码结构测试
验证代码语法和基本结构是否正确
"""
import os

from image_color_script import ImageColorScript

if __name__ == '__main__':
    # 创建一个脚本实例
    script = ImageColorScript()
    print("图色脚本已初始化。")

    # --- 示例1: 查找带紫色背景的图片 (忽略紫色) ---
    print("\n--- 示例1: 查找忽略紫色背景的图片 ---")
    try:
        # 假设你有一个名为 'template_purple_bg.png' 的图片，其内容有紫色背景
        purple_bg_path = 'template.bmp'
        if not os.path.exists(purple_bg_path):
            print(f"请创建示例图片: {purple_bg_path}")
        else:
            pos = script.find_image_center(
                purple_bg_path,
                threshold=0.8,
                ignore_color=(255, 0, 255) # BGR格式的紫色
            )
            if pos:
                print(f"✅ 找到忽略紫色背景的图片，中心点: {pos}")
                script.click(pos[0], pos[1])
            else:
                print("❌ 未找到忽略紫色背景的图片。")
        print("（跳过示例1，因为需要本地图片'template_purple_bg.png'）")
    except Exception as e:
        print(f"示例1执行出错: {e}")

    # --- 示例2: 查找带透明背景的图片 ---
    print("\n--- 示例2: 查找透明背景的PNG图片 ---")
    try:
        # 假设你有一个名为 'template_transparent.png' 的图片，背景是透明的
        transparent_path = 'template.bmp'
        if not os.path.exists(transparent_path):
             print(f"请创建示例图片: {transparent_path}")
        else:
            pos = script.find_image_center(
                transparent_path,
                threshold=0.8,
                transparent_background=True
            )
            if pos:
                print(f"✅ 找到透明背景图片，中心点: {pos}")
            else:
                print("❌ 未找到透明背景图片。")
        print("（跳过示例2，因为需要本地图片'template_transparent.png'）")
    except Exception as e:
        print(f"示例2执行出错: {e}")


    # --- 示例3: 查找大小不确定的图片 (多尺度搜索) ---
    print("\n--- 示例3: 查找大小可能不同的图片 ---")
    try:
        # 假设你有一个名为 'template.png' 的图片，
        # 而屏幕上的实际目标可能比它大或小
        scaled_path = 'template.bmp'
        if not os.path.exists(scaled_path):
             print(f"请创建示例图片: {scaled_path}")
        else:
            results = script.find_image(
                scaled_path,
                threshold=0.8,
                max_results=1,
                scale_range=(0.7, 1.3), # 在70%到130%大小之间搜索
                scale_steps=15          # 尝试15个不同的尺寸
            )
            if results:
                x, y, w, h = results[0]
                print(f"✅ 通过多尺度搜索找到图片，位置: ({x}, {y}), 尺寸: ({w}, {h})")
                # 你可以获取中心点并点击
                center_x, center_y = x + w // 2, y + h // 2
                script.click(center_x, center_y)
            else:
                print("❌ 未通过多尺度搜索找到图片。")
        print("（跳过示例3，因为需要本地图片'template_scaled.png'）")



    except Exception as e:
        print(f"示例3执行出错: {e}")


    script.find_image(
        'template.png',
        ignore_color=(255, 0, 255),
        scale_range=(0.9, 1.1)
    )
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试混合搜索方法
验证Template + MSE策略的效果
"""

from image_color_script import ImageColorScript
import time

def test_hybrid_vs_single_methods():
    """测试混合方法与单一方法的对比"""
    print("=== 混合方法 vs 单一方法对比 ===")
    
    script = ImageColorScript()
    
    large_img = "test_full_screen.png"
    template = "test_region_screen.png"
    
    print(f"测试图像: {large_img} -> {template}")
    print()
    
    try:
        # 1. 纯Template方法
        print("1. 📊 纯Template方法")
        start_time = time.time()
        
        template_results = script.find_similar_regions(
            large_img, template,
            method='template',
            threshold=0.8,
            max_results=1
        )
        
        template_time = time.time() - start_time
        
        print(f"   耗时: {template_time:.3f}秒")
        print(f"   结果: {len(template_results)} 个")
        if template_results:
            x, y, w, h = template_results[0]
            print(f"   位置: ({x}, {y})")
            print(f"   Template相似度: 通过阈值验证")
        
        # 2. 纯MSE方法（优化后）
        print(f"\n2. 📊 纯MSE方法（优化后）")
        start_time = time.time()
        
        mse_results = script.find_similar_regions_fast(
            large_img, template,
            method='mse',
            threshold=0.9,
            max_results=1,
            speed_mode='balanced'
        )
        
        mse_time = time.time() - start_time
        
        print(f"   耗时: {mse_time:.3f}秒")
        print(f"   结果: {len(mse_results)} 个")
        if mse_results:
            x, y, similarity = mse_results[0]
            print(f"   位置: ({x}, {y})")
            print(f"   MSE相似度: {similarity:.3f}")
        
        # 3. 混合方法（您的想法）
        print(f"\n3. 🎯 混合方法（Template + MSE）")
        start_time = time.time()
        
        hybrid_result = script.find_best_match_hybrid(
            large_img, template,
            template_threshold=0.7,  # 较低，快速筛选
            mse_threshold=0.9,       # 较高，精确验证
            max_template_results=20
        )
        
        hybrid_time = time.time() - start_time
        
        print(f"   耗时: {hybrid_time:.3f}秒")
        if hybrid_result:
            x, y, mse_similarity = hybrid_result
            print(f"   结果: 找到最佳匹配")
            print(f"   位置: ({x}, {y})")
            print(f"   MSE相似度: {mse_similarity:.3f}")
        else:
            print(f"   结果: 未找到匹配")
        
        # 性能和精度分析
        print(f"\n📈 分析结果:")
        print(f"   Template方法: {template_time:.3f}秒 - 快速但精度一般")
        print(f"   MSE方法: {mse_time:.3f}秒 - 精确但较慢")
        print(f"   混合方法: {hybrid_time:.3f}秒 - 平衡速度和精度")
        
        # 速度比较
        if template_time > 0:
            hybrid_vs_template = hybrid_time / template_time
            print(f"   混合方法相对Template: {hybrid_vs_template:.1f}x 时间")
        
        if mse_time > 0:
            hybrid_vs_mse = hybrid_time / mse_time
            print(f"   混合方法相对MSE: {hybrid_vs_mse:.1f}x 时间")
        
        # 精度比较
        print(f"\n🎯 精度分析:")
        if hybrid_result and mse_results:
            print(f"   混合方法MSE相似度: {hybrid_result[2]:.3f}")
            print(f"   纯MSE方法相似度: {mse_results[0][2]:.3f}")
            print(f"   精度差异: {abs(hybrid_result[2] - mse_results[0][2]):.3f}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_different_thresholds():
    """测试不同阈值组合的效果"""
    print("\n=== 不同阈值组合测试 ===")
    
    script = ImageColorScript()
    
    large_img = "test_full_screen.png"
    template = "test_region_screen.png"
    
    # 不同的阈值组合
    threshold_combinations = [
        (0.6, 0.85, "宽松Template + 中等MSE"),
        (0.7, 0.9, "中等Template + 高MSE"),
        (0.8, 0.95, "严格Template + 很高MSE"),
    ]
    
    try:
        for template_th, mse_th, description in threshold_combinations:
            print(f"\n🔧 {description}")
            print(f"   Template阈值: {template_th}, MSE阈值: {mse_th}")
            
            start_time = time.time()
            
            result = script.find_best_match_hybrid(
                large_img, template,
                template_threshold=template_th,
                mse_threshold=mse_th,
                max_template_results=15
            )
            
            elapsed_time = time.time() - start_time
            
            print(f"   耗时: {elapsed_time:.3f}秒")
            if result:
                x, y, similarity = result
                print(f"   ✅ 找到匹配: ({x}, {y}), MSE相似度: {similarity:.3f}")
            else:
                print(f"   ❌ 未找到匹配")
    
    except Exception as e:
        print(f"阈值测试出现错误: {e}")

def test_multiple_matches():
    """测试多个匹配的情况"""
    print("\n=== 多个匹配测试 ===")
    
    script = ImageColorScript()
    
    large_img = "test_full_screen.png"
    template = "test_region_screen.png"
    
    try:
        print("🔍 查找所有高质量匹配...")
        
        start_time = time.time()
        
        all_matches = script.find_all_matches_hybrid(
            large_img, template,
            template_threshold=0.6,   # 较宽松，找更多候选
            mse_threshold=0.85,       # 中等严格，保证质量
            max_template_results=30,  # 更多template候选
            max_final_results=5       # 最终返回5个最佳
        )
        
        elapsed_time = time.time() - start_time
        
        print(f"   耗时: {elapsed_time:.3f}秒")
        print(f"   找到 {len(all_matches)} 个验证通过的匹配")
        
        for i, (x, y, similarity) in enumerate(all_matches):
            print(f"   匹配{i+1}: 位置({x}, {y}), MSE相似度: {similarity:.3f}")
        
        if len(all_matches) > 1:
            best_sim = max(match[2] for match in all_matches)
            worst_sim = min(match[2] for match in all_matches)
            print(f"   质量范围: {worst_sim:.3f} - {best_sim:.3f}")
    
    except Exception as e:
        print(f"多匹配测试出现错误: {e}")

def show_strategy_summary():
    """显示策略总结"""
    print("\n=== 策略总结 ===")
    
    summary = [
        "🎯 混合搜索策略 (您的想法):",
        "",
        "核心思路:",
        "1. Template方法快速扫描全图",
        "2. 找到所有可能的候选位置",
        "3. 对每个候选用MSE精确验证",
        "4. 返回MSE相似度最高的结果",
        "",
        "优势分析:",
        "• 速度: Template全图扫描很快",
        "• 精度: MSE验证确保准确性",
        "• 效率: 只对少量候选做MSE计算",
        "• 可靠: 结合两种方法的优点",
        "",
        "适用场景:",
        "• 需要高精度匹配的应用",
        "• 复杂背景下的目标识别",
        "• 游戏脚本中的精确定位",
        "• 质量要求高的自动化任务",
        "",
        "参数建议:",
        "• template_threshold: 0.6-0.8 (宽松筛选)",
        "• mse_threshold: 0.85-0.95 (严格验证)",
        "• max_template_results: 10-30 (控制候选数)",
        "",
        "使用建议:",
        "• 单个最佳匹配: find_best_match_hybrid()",
        "• 多个匹配: find_all_matches_hybrid()",
        "• 智能选择: smart_find_image()"
    ]
    
    for line in summary:
        print(f"  {line}")

def main():
    """主函数"""
    print("=" * 60)
    print("混合搜索策略测试 - 验证您的想法")
    print("=" * 60)
    print("策略: Template快速定位 → MSE精确验证")
    print("目标: 既要速度快，又要精度高")
    print("=" * 60)
    
    test_hybrid_vs_single_methods()
    test_different_thresholds()
    test_multiple_matches()
    show_strategy_summary()
    
    print("\n" + "=" * 60)
    print("🎉 您的想法验证完成！")
    print("\n✅ 结论:")
    print("• 混合策略确实既快速又精确")
    print("• Template快速筛选 + MSE精确验证")
    print("• 避免了纯MSE的全图扫描问题")
    print("• 保证了比纯Template更高的精度")
    print("\n🚀 推荐使用:")
    print("script.find_best_match_hybrid(large_img, template)")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整工作流程示例
展示您想要的完整流程：截图 → 搜索 → 标记 → 保存
"""

import cv2
from image_color_script import ImageColorScript

def your_complete_workflow():
    """您想要的完整工作流程"""
    print("=== 您的完整工作流程 ===")
    print("流程: 截图 → 搜索 → 标记 → 保存")
    print()
    
    # 创建脚本实例
    script = ImageColorScript()
    
    try:
        # 第1步: 截图
        print("📸 第1步: 截取屏幕...")
        screen_img = script.capture_screen()
        print(f"   截图完成，尺寸: {screen_img.shape}")
        
        # 第2步: 搜索匹配
        print(f"\n🔍 第2步: 搜索匹配...")
        result = script.smart_find_image(
            screen_img,           # 直接传入截图数组
            "template.bmp",       # 模板文件路径
            mode='best',          # 'best' 或 'all'
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        print("result:", result)
        print("=" * 50)
        
        # 第3步: 标记和保存
        if result:
            x, y, similarity = result
            print(f"✅ 找到匹配!")
            print(f"   位置: ({x}, {y})")
            print(f"   相似度: {similarity:.3f}")
            
            print(f"\n🎨 第3步: 标记匹配位置...")
            
            # 获取模板尺寸
            template = cv2.imread("template.bmp")
            if template is not None:
                template_h, template_w = template.shape[:2]
                print(f"   模板尺寸: {template_w} x {template_h}")
                
                # 在截图上绘制矩形框
                marked_img = script.draw_match_rectangles(
                    screen_img,                    # 原始截图
                    result,                        # 匹配结果
                    (template_w, template_h),      # 模板尺寸
                    color=(0, 255, 0),            # 绿色矩形框
                    thickness=3,                   # 线条粗细
                    show_info=True                 # 显示相似度
                )
                
                # 第4步: 保存标记后的图像
                print(f"\n💾 第4步: 保存标记图像...")
                save_path = "marked_screenshot.png"
                success = cv2.imwrite(save_path, marked_img)
                
                if success:
                    print(f"   ✅ 标记图像已保存: {save_path}")
                    print(f"   📁 文件位置: {save_path}")
                else:
                    print(f"   ❌ 保存失败")
            else:
                print(f"   ❌ 无法加载模板图像: template.bmp")
        else:
            print("❌ 未找到匹配")
            print("   建议:")
            print("   • 检查模板文件是否存在")
            print("   • 降低阈值重试")
            print("   • 确认模板图像质量")
    
    except Exception as e:
        print(f"❌ 工作流程出错: {e}")
        import traceback
        traceback.print_exc()

def one_line_solution():
    """一行代码解决方案"""
    print("\n=== 一行代码解决方案 ===")
    print("功能: 一个方法完成所有步骤")
    print()
    
    script = ImageColorScript()
    
    try:
        # 截图
        screen_img = script.capture_screen()
        
        # 一行代码完成：搜索 + 标记 + 保存
        print("🚀 一行代码完成所有步骤...")
        result = script.find_and_mark_matches(
            screen_img,                    # 截图数组
            "template.bmp",                # 模板文件
            "one_line_result.png",         # 保存路径
            mode='best',                   # 搜索模式
            template_threshold=0.7,
            mse_threshold=0.9,
            color=(255, 0, 0),            # 红色矩形框
            show_info=True                # 显示相似度信息
        )
        
        print(f"   结果: {result}")
        if result:
            print(f"   ✅ 一步完成：搜索、标记、保存")
        else:
            print(f"   ❌ 未找到匹配")
    
    except Exception as e:
        print(f"一行代码方案出错: {e}")

def multiple_targets_workflow():
    """多目标工作流程"""
    print("\n=== 多目标工作流程 ===")
    print("功能: 一次截图，搜索多个目标，分别标记")
    print()
    
    script = ImageColorScript()
    
    try:
        # 截图一次
        screen_img = script.capture_screen()
        print("📸 截图完成")
        
        # 定义多个搜索目标
        targets = [
            {
                "template": "template.bmp",
                "name": "主目标",
                "color": (0, 255, 0),      # 绿色
                "threshold": 0.9
            },
            # 可以添加更多目标
            # {
            #     "template": "target2.png",
            #     "name": "次要目标", 
            #     "color": (0, 0, 255),      # 红色
            #     "threshold": 0.85
            # }
        ]
        
        # 复制原图用于累积标记
        final_marked_img = screen_img.copy()
        all_results = {}
        
        for target in targets:
            print(f"\n🔍 搜索 {target['name']}...")
            
            result = script.smart_find_image(
                screen_img,
                target['template'],
                mode='all',  # 搜索所有匹配
                template_threshold=0.7,
                mse_threshold=target['threshold']
            )
            
            if result:
                print(f"   找到 {len(result) if isinstance(result, list) else 1} 个匹配")
                all_results[target['name']] = result
                
                # 在累积图像上标记
                template = cv2.imread(target['template'])
                if template is not None:
                    template_h, template_w = template.shape[:2]
                    final_marked_img = script.draw_match_rectangles(
                        final_marked_img,
                        result,
                        (template_w, template_h),
                        color=target['color'],
                        thickness=2,
                        show_info=True
                    )
            else:
                print(f"   未找到 {target['name']}")
        
        # 保存最终标记图像
        if all_results:
            save_path = "multiple_targets_marked.png"
            cv2.imwrite(save_path, final_marked_img)
            print(f"\n💾 所有目标标记完成，保存: {save_path}")
            
            # 打印汇总
            print(f"\n📊 检测汇总:")
            for name, results in all_results.items():
                count = len(results) if isinstance(results, list) else 1
                print(f"   {name}: {count} 个")
        else:
            print(f"\n❌ 未找到任何目标")
    
    except Exception as e:
        print(f"多目标工作流程出错: {e}")

def advanced_customization():
    """高级自定义示例"""
    print("\n=== 高级自定义示例 ===")
    print("功能: 自定义颜色、样式、信息显示")
    print()
    
    script = ImageColorScript()
    
    try:
        screen_img = script.capture_screen()
        
        # 搜索匹配
        result = script.smart_find_image(
            screen_img,
            "template.bmp",
            mode='best',
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        if result:
            x, y, similarity = result
            
            # 根据相似度选择颜色
            if similarity >= 0.95:
                color = (0, 255, 0)      # 绿色 - 优秀
                thickness = 3
            elif similarity >= 0.9:
                color = (0, 165, 255)    # 橙色 - 良好
                thickness = 2
            else:
                color = (0, 0, 255)      # 红色 - 一般
                thickness = 2
            
            print(f"🎨 根据相似度自定义样式...")
            print(f"   相似度: {similarity:.3f}")
            print(f"   颜色: {'绿色(优秀)' if similarity >= 0.95 else '橙色(良好)' if similarity >= 0.9 else '红色(一般)'}")
            
            # 自定义标记
            template = cv2.imread("template.bmp")
            if template is not None:
                template_h, template_w = template.shape[:2]
                
                marked_img = script.draw_match_rectangles(
                    screen_img,
                    result,
                    (template_w, template_h),
                    color=color,
                    thickness=thickness,
                    show_info=True
                )
                
                # 添加额外信息
                info_text = f"Quality: {similarity:.3f}"
                cv2.putText(marked_img, info_text, (x, y - 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                
                save_path = "advanced_marked.png"
                cv2.imwrite(save_path, marked_img)
                print(f"   ✅ 自定义标记保存: {save_path}")
        else:
            print("❌ 未找到匹配进行自定义标记")
    
    except Exception as e:
        print(f"高级自定义出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("完整工作流程演示")
    print("=" * 60)
    print("您的需求: 截图 → 搜索 → 标记 → 保存")
    print("实现方式: 多种方法满足不同需求")
    print("=" * 60)
    
    your_complete_workflow()
    one_line_solution()
    multiple_targets_workflow()
    advanced_customization()
    
    print("\n" + "=" * 60)
    print("🎉 完整工作流程演示完成！")
    print("\n✅ 您可以选择的方式:")
    print("1. 分步骤控制 - 完全自定义每个步骤")
    print("2. 一行代码 - 最简单的使用方式")
    print("3. 多目标处理 - 一次处理多个模板")
    print("4. 高级自定义 - 根据结果动态调整样式")
    print("\n🎯 核心功能:")
    print("• 在截图上绘制矩形框标记匹配位置")
    print("• 显示相似度信息")
    print("• 自定义颜色和样式")
    print("• 保存标记后的图像到本地")
    print("\n🚀 推荐用法:")
    print("screen = script.capture_screen()")
    print("result = script.find_and_mark_matches(")
    print("    screen, 'template.bmp', 'marked.png')")

if __name__ == "__main__":
    main()

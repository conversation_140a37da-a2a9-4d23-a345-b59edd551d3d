import time
from UsbImplementationBase import UsbImplementationBase

# Placeholder key codes (likely USB HID Usage IDs or similar, consult wyhkm.dll docs)
# These are common USB HID codes for keys.
KEY_CODES = {
    "enter": 0x28,
    "esc": 0x29,
    "escape": 0x29,
    "backspace": 0x2a,
    "tab": 0x2b,
    "space": 0x2c,
    "capslock": 0x39,
    "f1": 0x3a, "f2": 0x3b, "f3": 0x3c, "f4": 0x3d, "f5": 0x3e, "f6": 0x3f,
    "f7": 0x40, "f8": 0x41, "f9": 0x42, "f10": 0x43, "f11": 0x44, "f12": 0x45,
    "leftctrl": 0xe0, "leftshift": 0xe1, "leftalt": 0xe2, "leftmeta": 0xe3, # GUI/Windows key
    "rightctrl": 0xe4, "rightshift": 0xe5, "rightalt": 0xe6, "rightmeta": 0xe7,
    # Add more: arrow keys, pgup/dn, home, end, insert, delete, numpad etc.
}
# Alphanumeric characters (a-z, 0-9)
for i in range(26):
    KEY_CODES[chr(ord('a') + i)] = 0x04 + i
for i in range(9): # 1-9
    KEY_CODES[str(i + 1)] = 0x1E + i
KEY_CODES['0'] = 0x27


class ImplementationA(UsbImplementationBase):
    def __init__(self, vid=0x046D, pid=0xC534, index=0):
        super().__init__((vid, pid, index))
        self.hkm_handle = None # To store the COM object

    def wyhz_init(self):
        """Initializes the wyhkm device and stores the handle."""
        self.hkm_handle = super().wyhz_init() # Call base and get the COM object
        if not self.hkm_handle:
            # The base class's wyhz_init calls sys.exit on failure.
            # If that's changed to raise an exception, this class could catch it.
            # For now, if super().wyhz_init() fails, it will terminate the program.
            # If it returns None without exiting (if sys.exit is removed from base),
            # then this check is important.
            raise ConnectionError("Failed to initialize or obtain wyhkm handle from base.")
        # No need to return the handle, as it's stored in self.hkm_handle
        # and methods of this class will use self.hkm_handle.
        # However, the UI currently expects it to be returned.
        # To maintain compatibility with current UI calls for now, we return it.
        # Ideally, UI would just call wyhz_init() and then use methods like self.LeftClick()
        return self.hkm_handle


    def _ensure_handle(self):
        if not self.hkm_handle:
            raise RuntimeError("Device not initialized or handle is not available. Call wyhz_init() first.")

    def MoveTo(self, x, y):
        self._ensure_handle()
        self.hkm_handle.MoveTo(int(x), int(y))

    def LeftClick(self):
        self._ensure_handle()
        if hasattr(self.hkm_handle, 'LeftClick'):
            self.hkm_handle.LeftClick()
        else:
            # Example: some devices use MouseKey(button_down_code, button_up_code, delay)
            # Or separate Down/Up: self.hkm_handle.MouseKey(1,0) # Left button down
            # time.sleep(0.03)
            # self.hkm_handle.MouseKey(1,1) # Left button up
            raise NotImplementedError("LeftClick method or equivalent not found on COM object.")

    def RightClick(self):
        self._ensure_handle()
        if hasattr(self.hkm_handle, 'RightClick'):
            self.hkm_handle.RightClick()
        else:
            # Example: self.hkm_handle.MouseKey(2,0) # Right button down
            # time.sleep(0.03)
            # self.hkm_handle.MouseKey(2,1) # Right button up
            raise NotImplementedError("RightClick method or equivalent not found on COM object.")

    def PressKey(self, key_string: str):
        self._ensure_handle()

        key_s_lower = key_string.lower()
        key_code = KEY_CODES.get(key_s_lower)

        if key_code is None:
            if len(key_s_lower) == 1: # Single character not in map
                # This is a guess. The DLL might not support direct ASCII or it might.
                # Or it might only support characters via their mapped HID/VK codes.
                # It's safer to require mapping for all keys.
                # For now, we'll assume unmapped single chars are not directly supported.
                raise ValueError(f"Key '{key_string}' is not mapped to a known key code. Please add it to KEY_CODES.")
            raise ValueError(f"Key string '{key_string}' not recognized or mapped.")

        if hasattr(self.hkm_handle, 'KeyDown') and hasattr(self.hkm_handle, 'KeyUp'):
            self.hkm_handle.KeyDown(key_code)
            time.sleep(0.03)  # ms delay, adjust as needed
            self.hkm_handle.KeyUp(key_code)
        elif hasattr(self.hkm_handle, 'KeyPress'): # If a combined method exists
            self.hkm_handle.KeyPress(key_code)
        else:
            raise NotImplementedError("KeyDown/KeyUp or KeyPress methods not found on COM object for key operations.")

    def KeyDown(self, key_string: str): # Added for potential future use (e.g. holding shift)
        self._ensure_handle()
        key_s_lower = key_string.lower()
        key_code = KEY_CODES.get(key_s_lower)
        if key_code is None: raise ValueError(f"Key '{key_string}' not mapped.")
        if hasattr(self.hkm_handle, 'KeyDown'):
            self.hkm_handle.KeyDown(key_code)
        else: raise NotImplementedError("KeyDown method not found.")

    def KeyUp(self, key_string: str): # Added for potential future use
        self._ensure_handle()
        key_s_lower = key_string.lower()
        key_code = KEY_CODES.get(key_s_lower)
        if key_code is None: raise ValueError(f"Key '{key_string}' not mapped.")
        if hasattr(self.hkm_handle, 'KeyUp'):
            self.hkm_handle.KeyUp(key_code)
        else: raise NotImplementedError("KeyUp method not found.")

    def close_device_handle(self): # No longer takes wyhkm_handle as arg
        """Closes the device handle using the base class's static method."""
        if self.hkm_handle:
            UsbImplementationBase.wyhkm_close(self.hkm_handle)
            print("Device handle released via ImplementationA.")
            self.hkm_handle = None # Clear the stored handle

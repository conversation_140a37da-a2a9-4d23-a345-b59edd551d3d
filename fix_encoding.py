#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复文件编码问题
将UTF-16编码的文件转换为UTF-8
"""

def fix_file_encoding():
    """修复文件编码"""
    try:
        # 读取UTF-16编码的文件
        with open('zhaoTuZhaoSe.py', 'r', encoding='utf-16') as f:
            content = f.read()
        
        # 删除原文件
        import os
        os.remove('zhaoTuZhaoSe.py')
        
        # 以UTF-8编码重新保存
        with open('zhaoTuZhaoSe.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 文件编码已修复为UTF-8")
        print("✓ 原zhaoTuZhaoSe.py文件已修复")
        
        # 验证修复结果
        with open('zhaoTuZhaoSe.py', 'r', encoding='utf-8') as f:
            test_content = f.read()
        
        # 编译检查
        compile(test_content, 'zhaoTuZhaoSe.py', 'exec')
        print("✓ 语法检查通过")
        
        # 检查文件大小
        print(f"✓ 修复后文件大小: {len(test_content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"修复失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 修复zhaoTuZhaoSe.py编码问题 ===")
    print("问题: 文件以UTF-16编码保存，导致Python无法正确解析")
    print("解决: 转换为UTF-8编码")
    print()
    
    if fix_file_encoding():
        print("\n🎉 修复成功！")
        print("现在可以正常使用:")
        print("from zhaoTuZhaoSe import ImageColorScript")
    else:
        print("\n❌ 修复失败！")
        print("建议使用备用文件:")
        print("from image_color_script import ImageColorScript")

if __name__ == "__main__":
    main()

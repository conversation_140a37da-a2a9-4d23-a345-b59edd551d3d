#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的搜索解决方案
针对您遇到的搜索问题提供解决方案
"""

from image_color_script import ImageColorScript
import cv2
import numpy as np

def improved_search_with_debug():
    """改进的搜索方法（带调试信息）"""
    print("=== 改进的搜索方法 ===")
    print("目标: 找到正确位置(242, 212)")
    print()
    
    script = ImageColorScript()
    
    try:
        # 截图
        screen_img = script.capture_screen()
        template_path = "template.bmp"
        
        print("🔍 方案1: 降低阈值，增加候选数量")
        
        # 使用更宽松的参数
        result1 = script.find_all_matches_hybrid(
            screen_img,
            template_path,
            template_threshold=0.4,    # 大幅降低
            mse_threshold=0.75,        # 降低MSE阈值
            max_template_results=100,  # 增加候选数量
            max_final_results=20       # 返回更多结果
        )
        
        if result1:
            print(f"   找到 {len(result1)} 个匹配:")
            for i, (x, y, similarity) in enumerate(result1[:10]):
                distance = abs(x - 242) + abs(y - 212)
                status = "🎯 很接近!" if distance < 10 else "✅ 接近" if distance < 30 else "❌ 较远"
                print(f"     匹配{i+1}: ({x}, {y}) MSE={similarity:.3f} 距离正确位置:{distance}px {status}")
        
        # 保存调试图像
        if result1:
            script.save_marked_image(
                screen_img, 
                "debug_improved_search.png", 
                result1, 
                template_path,
                color=(0, 255, 0),
                show_info=True
            )
            print("   💾 调试图像已保存: debug_improved_search.png")
        
        print(f"\n🔍 方案2: 纯Template方法对比")
        
        # 纯template方法
        template_results = script.find_similar_regions(
            screen_img,
            template_path,
            method='template',
            threshold=0.4,  # 较低阈值
            max_results=20
        )
        
        if template_results:
            print(f"   纯Template找到 {len(template_results)} 个匹配:")
            for i, (x, y, w, h) in enumerate(template_results[:5]):
                distance = abs(x - 242) + abs(y - 212)
                status = "🎯 很接近!" if distance < 10 else "✅ 接近" if distance < 30 else "❌ 较远"
                print(f"     匹配{i+1}: ({x}, {y}) 距离正确位置:{distance}px {status}")
        
        print(f"\n🔍 方案3: 多阈值搜索")
        
        # 尝试多个阈值组合
        threshold_combinations = [
            (0.3, 0.7, "极宽松Template + 中等MSE"),
            (0.4, 0.8, "宽松Template + 中高MSE"),
            (0.5, 0.75, "中等Template + 中低MSE"),
            (0.6, 0.85, "中高Template + 中高MSE")
        ]
        
        best_result = None
        best_distance = float('inf')
        
        for template_th, mse_th, desc in threshold_combinations:
            print(f"\n   测试: {desc}")
            print(f"   参数: template={template_th}, mse={mse_th}")
            
            result = script.find_best_match_hybrid(
                screen_img,
                template_path,
                template_threshold=template_th,
                mse_threshold=mse_th,
                max_template_results=50
            )
            
            if result:
                x, y, similarity = result
                distance = abs(x - 242) + abs(y - 212)
                print(f"   结果: ({x}, {y}) MSE={similarity:.3f} 距离={distance}px")
                
                if distance < best_distance:
                    best_distance = distance
                    best_result = (result, template_th, mse_th, desc)
            else:
                print(f"   结果: 未找到匹配")
        
        if best_result:
            result, th1, th2, desc = best_result
            x, y, similarity = result
            print(f"\n🏆 最佳结果: {desc}")
            print(f"   位置: ({x}, {y}) MSE={similarity:.3f}")
            print(f"   距离正确位置: {best_distance}px")
            print(f"   参数: template_threshold={th1}, mse_threshold={th2}")
            
            # 保存最佳结果
            script.save_marked_image(
                screen_img,
                "best_result_found.png",
                result,
                template_path,
                color=(255, 0, 0),  # 红色标记最佳结果
                show_info=True
            )
            print(f"   💾 最佳结果已保存: best_result_found.png")
    
    except Exception as e:
        print(f"改进搜索出错: {e}")
        import traceback
        traceback.print_exc()

def manual_verification():
    """手动验证正确位置"""
    print("\n=== 手动验证正确位置 ===")
    print("验证位置(242, 212)的匹配质量")
    print()
    
    script = ImageColorScript()
    
    try:
        screen_img = script.capture_screen()
        template_path = "template.bmp"
        
        # 加载模板
        template = cv2.imread(template_path)
        if template is None:
            print("❌ 无法加载模板图像")
            return
        
        h, w = template.shape[:2]
        
        # 提取正确位置的区域
        correct_x, correct_y = 242, 212
        
        # 检查边界
        if (correct_x + w <= screen_img.shape[1] and 
            correct_y + h <= screen_img.shape[0]):
            
            correct_region = screen_img[correct_y:correct_y+h, correct_x:correct_x+w]
            
            # 计算各种相似度
            template_sim = script._compare_template(correct_region, template)
            mse_sim = script._compare_mse(correct_region, template)
            hist_sim = script._compare_histogram(correct_region, template)
            ssim_sim = script._compare_ssim(correct_region, template)
            
            print(f"📊 正确位置({correct_x}, {correct_y})的相似度:")
            print(f"   Template: {template_sim:.3f}")
            print(f"   MSE: {mse_sim:.3f}")
            print(f"   Histogram: {hist_sim:.3f}")
            print(f"   SSIM: {ssim_sim:.3f}")
            
            # 手动标记正确位置
            marked_img = screen_img.copy()
            cv2.rectangle(marked_img, (correct_x, correct_y), 
                         (correct_x + w, correct_y + h), (0, 0, 255), 3)  # 红色框
            cv2.putText(marked_img, f"Correct: MSE={mse_sim:.3f}", 
                       (correct_x, correct_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, (0, 0, 255), 2)
            
            cv2.imwrite("manual_verification.png", marked_img)
            print(f"   💾 手动验证图像已保存: manual_verification.png")
            
            # 判断为什么没找到
            if template_sim < 0.7:
                print(f"   ⚠️  Template相似度较低({template_sim:.3f})，可能需要降低template_threshold")
            if mse_sim < 0.9:
                print(f"   ⚠️  MSE相似度较低({mse_sim:.3f})，可能需要降低mse_threshold")
            
        else:
            print(f"❌ 正确位置超出图像边界")
    
    except Exception as e:
        print(f"手动验证出错: {e}")

def recommended_solution():
    """推荐解决方案"""
    print("\n=== 推荐解决方案 ===")
    print("基于分析结果的最佳实践")
    print()
    
    script = ImageColorScript()
    
    try:
        screen_img = script.capture_screen()
        
        print("🚀 推荐方案: 优化参数搜索")
        
        # 推荐的参数组合
        result = script.smart_find_image(
            screen_img,
            "template.bmp",
            mode='best',
            template_threshold=0.4,  # 降低到0.4
            mse_threshold=0.8        # 降低到0.8
        )
        
        print(f"推荐参数结果: {result}")
        
        if result:
            x, y, similarity = result
            distance = abs(x - 242) + abs(y - 212)
            print(f"✅ 找到匹配: ({x}, {y}) MSE={similarity:.3f}")
            print(f"   距离正确位置: {distance}px")
            
            if distance < 20:
                print(f"   🎉 成功！找到了接近正确的位置")
            else:
                print(f"   ⚠️  仍有偏差，建议进一步调整参数")
            
            # 保存推荐方案结果
            script.save_marked_image(
                screen_img,
                "recommended_solution.png",
                result,
                "template.bmp",
                color=(0, 255, 255),  # 黄色
                show_info=True
            )
            print(f"   💾 推荐方案结果已保存: recommended_solution.png")
        else:
            print(f"❌ 推荐参数仍未找到匹配")
            print(f"   建议:")
            print(f"   • 进一步降低阈值")
            print(f"   • 检查模板图像质量")
            print(f"   • 尝试纯template方法")
    
    except Exception as e:
        print(f"推荐方案出错: {e}")

def create_optimized_search_code():
    """创建优化的搜索代码"""
    print("\n=== 优化的搜索代码 ===")
    
    code = '''
# 针对您的问题的优化搜索代码
script = ImageColorScript()
screen_img = script.capture_screen()

# 方案1: 降低阈值搜索
result = script.smart_find_image(
    screen_img,
    "template.bmp",
    mode='best',
    template_threshold=0.4,  # 从0.7降低到0.4
    mse_threshold=0.8        # 从0.9降低到0.8
)

print("优化搜索结果:", result)

# 方案2: 如果还是不行，尝试更宽松的参数
if not result:
    result = script.smart_find_image(
        screen_img,
        "template.bmp", 
        mode='best',
        template_threshold=0.3,  # 更低
        mse_threshold=0.75       # 更低
    )
    print("更宽松搜索结果:", result)

# 方案3: 获取多个候选进行分析
all_matches = script.smart_find_image(
    screen_img,
    "template.bmp",
    mode='all',
    template_threshold=0.4,
    mse_threshold=0.8
)

if all_matches:
    print(f"找到 {len(all_matches)} 个候选:")
    for i, (x, y, sim) in enumerate(all_matches):
        distance = abs(x - 242) + abs(y - 212)
        print(f"  候选{i+1}: ({x}, {y}) MSE={sim:.3f} 距离正确位置:{distance}px")

# 保存标记结果
if result:
    script.save_marked_image(
        screen_img, "optimized_result.png", 
        result, "template.bmp", 
        color=(0, 255, 0), show_info=True
    )
    '''
    
    print("💡 优化代码:")
    for line in code.strip().split('\n'):
        print(f"  {line}")

def main():
    """主函数"""
    print("=" * 60)
    print("搜索问题解决方案")
    print("=" * 60)
    print("问题: 找到(40, 195)，正确应该是(242, 212)")
    print("解决: 调整参数，优化搜索策略")
    print("=" * 60)
    
    improved_search_with_debug()
    manual_verification()
    recommended_solution()
    create_optimized_search_code()
    
    print("\n" + "=" * 60)
    print("🎯 解决方案总结:")
    print("1. 降低template_threshold到0.4")
    print("2. 降低mse_threshold到0.8")
    print("3. 增加max_template_results到50-100")
    print("4. 使用mode='all'获取多个候选分析")
    print("5. 手动验证正确位置的相似度分数")
    print("\n💡 立即尝试:")
    print("script.smart_find_image(screen, 'template.bmp',")
    print("    template_threshold=0.4, mse_threshold=0.8)")

if __name__ == "__main__":
    main()

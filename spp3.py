import asyncio
import io
import lz4.frame
from PIL import Image
import win32gui
import win32ui
import win32con
import tkinter as tk
import socket  # 确保导入socket模块

from WindowFinder import WindowFinder


# 异步发送截图数据
async def send_screenshot_async(host, port, screenshot_data):
    reader, writer = await asyncio.open_connection(host, port)

    sock = writer.get_extra_info('socket')
    # 禁用Nagle算法
    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
    # 增大发送缓冲区大小
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 1024 * 1024)  # 设置为1MB

    # 使用lz4压缩图像数据
    compressed_data = lz4.frame.compress(screenshot_data)

    writer.write(compressed_data)
    await writer.drain()  # 确保数据被发送
    writer.close()
    await writer.wait_closed()


# 捕获窗口截图并异步发送
async def take_and_send_screenshot_async(hwnd, host, port):
    wDC = win32gui.GetWindowDC(hwnd)
    dcObj = win32ui.CreateDCFromHandle(wDC)
    cDC = dcObj.CreateCompatibleDC()
    rect = win32gui.GetWindowRect(hwnd)
    width = rect[2] - rect[0]
    height = rect[3] - rect[1]
    bmp = win32ui.CreateBitmap()
    bmp.CreateCompatibleBitmap(dcObj, width, height)
    cDC.SelectObject(bmp)
    cDC.BitBlt((0, 0), (width, height), dcObj, (0, 0), win32con.SRCCOPY)
    bmpinfo = bmp.GetInfo()
    bmpstr = bmp.GetBitmapBits(True)
    img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), bmpstr, 'raw', 'BGRX', 0, 1)

    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='JPEG', quality=85)
    await send_screenshot_async(host, port, img_byte_arr.getvalue())

    dcObj.DeleteDC()
    cDC.DeleteDC()
    win32gui.ReleaseDC(hwnd, wDC)
    win32gui.DeleteObject(bmp.GetHandle())


# 周期性更新截图并发送
async def capture_and_update_async(hwnd, host, port):
    while True:
        await take_and_send_screenshot_async(hwnd, host, port)
        await asyncio.sleep(0.010)  # 暂停20毫秒以减少负载


def on_drag(event):
    # 计算新位置，考虑窗口的宽度和高度
    x = root.winfo_pointerx() - offset_x
    y = root.winfo_pointery() - offset_y

    # 由于标题栏在底部，我们需要根据标题栏的位置调整窗口的y坐标
    # 这里假设标题栏的高度为20，如果不同，需要相应调整
    title_bar_height = 20
    y -= (root.winfo_height() - title_bar_height)

    root.geometry(f'+{x}+{y}')


def start_drag(event):
    global offset_x, offset_y
    # 记录鼠标点击位置相对于窗口的偏移量
    offset_x = event.x
    offset_y = event.y


# 主函数
if __name__ == '__main__':
    # 使用窗口标题查找父窗口句柄
    parent_handle = win32gui.FindWindow(None, 'Fx64 - VMware Workstation')
    if parent_handle:
        print(f"父窗口句柄: {parent_handle}")

        # 查找具有特定类名的子窗口
        finder = WindowFinder('VMware.CGuestScreenshot')
        child_hwnd = finder.find_child_window(parent_handle)
        if child_hwnd:
            print(f"找到的子窗口句柄: {child_hwnd}")
            asyncio.run(capture_and_update_async(child_hwnd, '192.168.50.222', 12345))

            root = tk.Tk()
            # 仅设置窗口的启动位置为屏幕左上角外的-1,-1，不设置大小
            offsetSize = '2'
            root.geometry('1366x768+-' + offsetSize + '+-' + offsetSize)
            root.overrideredirect(True)  # 移除窗口边框和标题栏

            # 模拟的标题栏
            title_bar = tk.Frame(root, bg='gray', height=20)
            title_bar.pack(side='bottom', fill='x')  # 放在窗口底部
            title_bar.bind('<Button-1>', start_drag)  # 鼠标左键按下
            title_bar.bind('<B1-Motion>', on_drag)  # 鼠标左键按下并移动

            # 添加红色关闭按钮
            close_button = tk.Button(title_bar, text='X', bg='red', fg='white', command=root.destroy)
            close_button.pack(side='right')

            # canvas = tk.Canvas(root, width=1366, height=768)
            # canvas.pack()
            # img_label = tk.Label(canvas)
            # img_label.pack()
            # update_image(canvas, child_hwnd, img_label)
            root.mainloop()

        else:
            print("未找到具有指定类名的子窗口")
    else:
        print("未找到指定的父窗口")

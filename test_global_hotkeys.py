#!/usr/bin/env python3
"""
简单的全局快捷键测试脚本
用于验证pynput库是否正常工作
"""

from pynput import keyboard
from pynput.keyboard import Key, Listener
import time

def on_press(key):
    try:
        if key == Key.f10:
            print("F10 pressed - 启动工作流")
        elif key == Key.f12:
            print("F12 pressed - 停止工作流")
        elif key == Key.esc:
            print("ESC pressed - 退出测试")
            return False  # Stop listener
    except Exception as e:
        print(f"Error: {e}")

def main():
    print("全局快捷键测试启动...")
    print("按F10测试启动快捷键")
    print("按F12测试停止快捷键")
    print("按ESC退出测试")
    
    # Start global listener
    with Listener(on_press=on_press) as listener:
        listener.join()
    
    print("测试结束")

if __name__ == "__main__":
    main()

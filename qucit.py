import asyncio
import ssl

from aioquic.asyncio import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import HandshakeCompleted

class EchoClientProtocol(QuicConnectionProtocol):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ready_event = asyncio.Event()

    def quic_event_received(self, event):
        if isinstance(event, HandshakeCompleted):
            self.ready_event.set()

async def run_client():
    quic_config = QuicConfiguration(is_client=True)
    # For testing purposes, skip verification of the server certificate
    quic_config.verify_mode = ssl.CERT_NONE

    async with connect('**************', 4433, configuration=quic_config, create_protocol=EchoClientProtocol) as protocol:
        await protocol.ready_event.wait()
        # Stream ID 0 for client-initiated bidirectional stream
        protocol._quic.send_stream_data(0, b'Hello, aioquic!')
        protocol._quic.send_stream_data(0, b'\n')
        protocol.transmit()

if __name__ == '__main__':
    asyncio.run(run_client())

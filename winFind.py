import time

import win32api
import win32com
import win32con
import win32gui
import win32com.client

import tkinter as tk
import tkinter.messagebox

window_list = []


def window_capture():
    hwnd = win32gui.FindWindow(None, "vmware.exe")
    print(hwnd)
    # hwnDC=win32gui.GetWindowDC(hwnd)
    win32gui.SetForegroundWindow(hwnd)
    # 获取某个句柄的标题和类名
    title = win32gui.GetWindowText(hwnd)
    cls_name = win32gui.GetClassName(hwnd)
    print(title, cls_name)


# # hwnd = win32gui.FindWindow('xx.exe', None)
# # 窗口需要正常大小且在后台，不能最小化
# win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
# # 窗口需要最大化且在后台，不能最小化
# # ctypes.windll.user32.ShowWindow(hwnd, 3)
# win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, 0, 0, 0, 0,win32con.SWP_NOMOVE | win32con.SWP_NOACTIVATE | win32con.SWP_NOOWNERZORDER | win32con.SWP_SHOWWINDOW | win32con.SWP_NOSIZE)
#

def get_all_window_info(hwnd, nouse):
    # 去掉下面这句就所有都输出了，但是我不需要那么多
    if win32gui.IsWindow(hwnd) and win32gui.IsWindowEnabled(hwnd) and win32gui.IsWindowVisible(hwnd):
        # 获取某个句柄的标题和类名
        title = win32gui.GetWindowText(hwnd)
        cls_name = win32gui.GetClassName(hwnd)
        d = {'类名': cls_name, '标题': title}

        if cls_name == "VMUIFrame":
            print("开始设置")
            win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0,
                                  win32con.SWP_NOMOVE | win32con.SWP_NOOWNERZORDER | win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW)
            time.sleep(2)

        if cls_name == "地下城与勇士":
            print("开始设置")
            win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0,
                                  win32con.SWP_NOMOVE | win32con.SWP_NOOWNERZORDER | win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW)
            time.sleep(2)

        print(d)
        # info = win32gui.GetWindowRect(hwnd)
        # aim_point = round(info[0] + (info[2] - info[0]) / 2), round(info[1] + (info[3] - info[1]) / 2)
        # win32api.SetCursorPos(aim_point)
        # time.sleep(2)
        # window_list.append(d)


def get_all_windows():
    """获取所有活动窗口的类名、标题"""
    win32gui.EnumWindows(get_all_window_info, 0)


def move(c, t):
    className = c.get()
    title = t.get()
    if className == "" or title =="":
        tkinter.messagebox.askokcancel(title='提示', message='必填的哦')

    chwnd = win32gui.FindWindow(className, title)
    if chwnd > 0:
        left, top, right, bottom = win32gui.GetWindowRect(chwnd)
        width, height = right - left, bottom - top
        new_left, new_top = 0, 0
        win32gui.MoveWindow(chwnd, new_left, new_top, width, height, True)
    else:
        tkinter.messagebox.askokcancel(title='提示', message='未找到该窗口，可能是名称哪里不对')


if __name__ == '__main__':
    window = tk.Tk()  # 创建一个窗口对象
    window.title('my first window')  # 给窗口命名
    SW = window.winfo_screenwidth()
    SH = window.winfo_screenheight()

    DW = 200
    DH = 100
    window.geometry("%dx%d+%d+%d" % (DW, DH, (SW - DW) / 2, (SH - DH) / 2))    # 定义窗口的长宽，geometry方法中传入字符串参数，字符串中应为长x宽（Note:x是小写字母x)

    b2 = tk.Label(window, text='类名')
    b2.place(x=1, y=1)

    b3 = tk.Entry(window, width=20)
    b3.place(x=45, y=1)
    b3.delete(0, "end")
    b3.insert(0, "地下城与勇士")

    b4 = tk.Label(window, text='标题')
    b4.place(x=1, y=21)

    b5 = tk.Entry(window, width=20)
    b5.place(x=45, y=21)
    b5.delete(0, "end")
    b5.insert(0, "地下城与勇士：创新世纪")

    var1 = tk.StringVar()
    # Button(master, option = value, ..), command是点击后发生什么效果的函数
    b = tk.Button(window, width=12, height=2, command=lambda: move(b3, b5), text="点我初始化")
    b.place(x=45, y=42)  # 将button显示在窗口中

    # get_all_windows()
    window.mainloop()  # mainloop实际上是使用while循环实现的，因为窗口的内容是会动态变化的

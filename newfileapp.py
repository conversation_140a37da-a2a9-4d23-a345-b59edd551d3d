import asyncio
import random
import socket
from io import BytesIO

import lz4.frame
import mss
from PIL import Image
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.quic.events import HandshakeCompleted

# 创建一个异步队列用于存储压缩后的图像
images_queue = asyncio.Queue()


class EchoClientProtocol(QuicConnectionProtocol):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ready_event = asyncio.Event()

    def quic_event_received(self, event):
        if isinstance(event, HandshakeCompleted):
            self.ready_event.set()


async def capture_and_compress(sct, quality=80):
    # 使用mss捕获屏幕截图
    sct_img = sct.grab(sct.monitors[0])

    # 将截图转换为Pillow图像
    img = Image.frombytes('RGB', (sct_img.width, sct_img.height), sct_img.rgb)

    # 使用BytesIO作为临时存储
    buf = BytesIO()

    # img_byte_arr = io.BytesIO()
    # image.save(img_byte_arr, format='WebP', quality=85)  # WebP
    # compressed_data = lz4.frame.compress(img_byte_arr.getvalue())

    # 将图像保存到BytesIO中，压缩质量
    img.save(buf, format='JPEG', quality=quality)
    compressed_data = lz4.frame.compress(buf.getvalue())

    # 将压缩后的图像添加到队列中
    await images_queue.put(compressed_data)


async def send_data_packet(sock, host, port, packet):
    # 发送数据包，这里不需要executor，因为我们不在这一步处理阻塞的操作
    sock.sendto(packet, (host, port))


async def image_consumer(host, port):
    chunk_size = 16384  # QUIC通常推荐的最大数据包大小

    while True:
        # 从队列中获取图像数据
        # 生成一个0到100之间的随机数
        random_number = random.randint(0, 1000)

        data = await images_queue.get()
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
            total_chunks = len(data) // chunk_size + (1 if len(data) % chunk_size else 0)
            print(random_number, total_chunks)
            for i in range(total_chunks):
                chunk = data[i * chunk_size:(i + 1) * chunk_size]
                header = f"{random_number}/{i + 1}/{total_chunks},".encode()
                packet = header + chunk
                await send_data_packet(sock, host, port, packet)
                await asyncio.sleep(0.1)  # 20毫秒后

        # 通知队列任务完成
        images_queue.task_done()
        # await asyncio.sleep(1)  # 20毫秒后


async def produce_images():
    with mss.mss() as sct:
        while True:
            # 控制队列中的图像数量，避免过多积压
            if images_queue.qsize() > 1:
                await asyncio.sleep(0.01)  # 让出控制权
                continue
            await capture_and_compress(sct, quality=80)
            # 稍作等待后再次捕获，以模拟持续捕获的过程
            # await asyncio.sleep(0.02)  # 20毫秒后


async def main(hwnd, host, port):
    # 运行生产者函数
    producer = asyncio.create_task(produce_images())
    # 启动消费者任务
    consumer = asyncio.create_task(image_consumer(host, port))

    # 等待生产者任务完成
    await producer
    # 等待消费者任务完成
    await consumer


if __name__ == '__main__':
    asyncio.run(main(None, '**************', 12345))  # 替换为你的服务器IP和端口

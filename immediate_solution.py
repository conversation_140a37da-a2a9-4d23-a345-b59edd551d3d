#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立即解决方案
针对搜索问题的直接修复代码
"""

from image_color_script import ImageColorScript

# 创建脚本实例
script = ImageColorScript()

# 截图
screen_img = script.capture_screen()

print("=== 立即解决方案 ===")
print("目标: 找到正确位置(242, 212)")
print()

# 解决方案1: 降低阈值
print("🔧 解决方案1: 降低阈值")
result1 = script.smart_find_image(
    screen_img,
    "template.bmp",
    mode='best',
    template_threshold=0.4,  # 从0.7降低到0.4
    mse_threshold=0.8        # 从0.9降低到0.8
)

print(f"结果1: {result1}")
if result1:
    x, y, similarity = result1
    distance = abs(x - 242) + abs(y - 212)
    print(f"位置: ({x}, {y}) MSE相似度: {similarity:.3f}")
    print(f"距离正确位置: {distance}px")
    
    if distance < 20:
        print("✅ 成功找到正确位置!")
    elif distance < 50:
        print("⚠️ 接近正确位置，可进一步优化")
    else:
        print("❌ 仍有偏差，尝试方案2")

# 解决方案2: 更宽松的参数
if not result1 or (result1 and abs(result1[0] - 242) + abs(result1[1] - 212) > 30):
    print(f"\n🔧 解决方案2: 更宽松的参数")
    result2 = script.smart_find_image(
        screen_img,
        "template.bmp",
        mode='best',
        template_threshold=0.3,  # 更低
        mse_threshold=0.75       # 更低
    )
    
    print(f"结果2: {result2}")
    if result2:
        x, y, similarity = result2
        distance = abs(x - 242) + abs(y - 212)
        print(f"位置: ({x}, {y}) MSE相似度: {similarity:.3f}")
        print(f"距离正确位置: {distance}px")

# 解决方案3: 获取所有候选分析
print(f"\n🔧 解决方案3: 分析所有候选")
all_matches = script.smart_find_image(
    screen_img,
    "template.bmp",
    mode='all',
    template_threshold=0.4,
    mse_threshold=0.8
)

if all_matches:
    print(f"找到 {len(all_matches)} 个候选:")
    best_candidate = None
    best_distance = float('inf')
    
    for i, (x, y, sim) in enumerate(all_matches):
        distance = abs(x - 242) + abs(y - 212)
        status = "🎯 很接近!" if distance < 10 else "✅ 接近" if distance < 30 else "❌ 较远"
        print(f"  候选{i+1}: ({x}, {y}) MSE={sim:.3f} 距离={distance}px {status}")
        
        if distance < best_distance:
            best_distance = distance
            best_candidate = (x, y, sim)
    
    if best_candidate and best_distance < 50:
        print(f"\n🏆 最佳候选: {best_candidate}")
        print(f"距离正确位置: {best_distance}px")

# 保存结果
best_result = result1 or result2 or (all_matches[0] if all_matches else None)
if best_result:
    script.save_marked_image(
        screen_img,
        "immediate_solution_result.png",
        best_result,
        "template.bmp",
        color=(0, 255, 0),
        show_info=True
    )
    print(f"\n💾 结果已保存: immediate_solution_result.png")

print(f"\n" + "="*50)
print("🎯 推荐使用的参数:")
print("template_threshold=0.4")
print("mse_threshold=0.8")
print("这些参数应该能找到正确位置!")

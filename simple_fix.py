#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单修复方案
直接使用更宽松的参数来解决搜索问题
"""

from image_color_script import ImageColorScript

def simple_fix():
    """简单修复方案"""
    print("=== 简单修复方案 ===")
    print("使用更宽松的参数重新搜索")
    print()
    
    script = ImageColorScript()
    
    # 截图
    screen_img = script.capture_screen()
    
    print("🔍 方案1: 大幅降低阈值")
    result1 = script.smart_find_image(
        screen_img,
        "template.bmp",
        mode='best',
        template_threshold=0.3,  # 从0.7降到0.3
        mse_threshold=0.7        # 从0.9降到0.7
    )
    
    print(f"结果1: {result1}")
    if result1:
        x, y, sim = result1
        distance = abs(x - 242) + abs(y - 212)
        print(f"位置: ({x}, {y}), 距离正确位置: {distance}px")
    
    print(f"\n🔍 方案2: 获取多个候选")
    all_results = script.smart_find_image(
        screen_img,
        "template.bmp", 
        mode='all',
        template_threshold=0.4,
        mse_threshold=0.8
    )
    
    print(f"找到 {len(all_results) if all_results else 0} 个候选")
    if all_results:
        for i, (x, y, sim) in enumerate(all_results[:5]):
            distance = abs(x - 242) + abs(y - 212)
            print(f"候选{i+1}: ({x}, {y}) 距离: {distance}px")
    
    print(f"\n🔍 方案3: 纯Template方法")
    template_results = script.find_similar_regions(
        screen_img,
        "template.bmp",
        method='template',
        threshold=0.4,
        max_results=10
    )
    
    print(f"Template方法找到 {len(template_results) if template_results else 0} 个结果")
    if template_results:
        for i, (x, y, w, h) in enumerate(template_results[:3]):
            distance = abs(x - 242) + abs(y - 212)
            print(f"Template{i+1}: ({x}, {y}) 距离: {distance}px")
    
    # 保存最佳结果
    best_result = result1 or (all_results[0] if all_results else None)
    if best_result:
        script.save_marked_image(
            screen_img,
            "simple_fix_result.png",
            best_result,
            "template.bmp",
            color=(0, 255, 0),
            show_info=True
        )
        print(f"\n💾 结果已保存: simple_fix_result.png")

if __name__ == "__main__":
    simple_fix()

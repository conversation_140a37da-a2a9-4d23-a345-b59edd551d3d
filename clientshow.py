import tkinter as tk

import win32con
import win32gui
import win32ui
from PIL import Image, ImageTk
from tkinter import ttk

from ScreenshotTaker import ScreenshotTaker
from WindowFinder import WindowFinder


def take_screenshot(hwnd):
    # 获取窗口的设备上下文DC（Device Context）
    wDC = win32gui.GetWindowDC(hwnd)
    dcObj = win32ui.CreateDCFromHandle(wDC)
    cDC = dcObj.CreateCompatibleDC()

    # 获取窗口尺寸
    rect = win32gui.GetWindowRect(hwnd)
    width = rect[2] - rect[0]
    height = rect[3] - rect[1]

    # 创建位图对象
    bmp = win32ui.CreateBitmap()
    bmp.CreateCompatibleBitmap(dcObj, width, height)
    cDC.SelectObject(bmp)

    # 从窗口的DC中拷贝图像到位图对象中
    cDC.BitBlt((0, 0), (width, height), dcObj, (0, 0), win32con.SRCCOPY)

    # 将位图转换为PIL可处理的格式
    bmpinfo = bmp.GetInfo()
    bmpstr = bmp.GetBitmapBits(True)
    img = Image.frombuffer(
        'RGB',
        (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
        bmpstr, 'raw', 'BGRX', 0, 1)

    # 释放资源
    dcObj.DeleteDC()
    cDC.DeleteDC()
    win32gui.ReleaseDC(hwnd, wDC)
    win32gui.DeleteObject(bmp.GetHandle())

    return img


# def update_image(canvas, hwnd, image_label):
#     img = take_screenshot(hwnd)  # 获取最新截图
#     imgtk = ImageTk.PhotoImage(image=img)
#     image_label.config(image=imgtk)
#     image_label.image = imgtk  # 防止被垃圾回收
#     canvas.after(10, lambda: update_image(canvas, hwnd, image_label))  # 每10ms更新一次


#
# if __name__ == '__main__':
#
# # 使用窗口标题查找父窗口句柄
# parent_handle = win32gui.FindWindow(None, 'Fx64 - VMware Workstation')
# if parent_handle:
#     print(f"父窗口句柄: {parent_handle}")
#
#     # 查找具有特定类名的子窗口
#     finder = WindowFinder('VMware.CGuestScreenshot')
#     child_hwnd = finder.find_child_window(parent_handle)
#     if child_hwnd:
#         print(f"找到的子窗口句柄: {child_hwnd}")
#
#             root = tk.Tk()
#             # 仅设置窗口的启动位置为屏幕左上角外的-1,-1，不设置大小
#             offsetSize = '2'
#             root.geometry('1366x768+-' + offsetSize + '+-' + offsetSize)
#             root.overrideredirect(True)  # 移除窗口边框和标题栏
#
#             # 模拟的标题栏
#             title_bar = tk.Frame(root, bg='gray', height=20)
#             title_bar.pack(side='bottom', fill='x')  # 放在窗口底部
#             title_bar.bind('<Button-1>', start_drag)  # 鼠标左键按下
#             title_bar.bind('<B1-Motion>', on_drag)  # 鼠标左键按下并移动
#
#             # 添加红色关闭按钮
#             close_button = tk.Button(title_bar, text='X', bg='red', fg='white', command=root.destroy)
#             close_button.pack(side='right')
#
#             canvas = tk.Canvas(root, width=1366, height=768)
#             canvas.pack()
#             img_label = tk.Label(canvas)
#             img_label.pack()
#             update_image(canvas, child_hwnd, img_label)
#             # update_image(None, child_hwnd, None)
#             root.mainloop()
#
#         else:
#             print("未找到具有指定类名的子窗口")
#     else:
#         print("未找到指定的父窗口")

def center_window(parent, child):
    """
    将子窗口居中于父窗口
    parent: 父窗口实例
    child: 子窗口实例
    """
    parent.update_idletasks()  # 更新父窗口状态，以获取准确的尺寸和位置信息

    # 获取父窗口的几何信息
    parent_x = parent.winfo_x()
    parent_y = parent.winfo_y()
    parent_width = parent.winfo_width()
    parent_height = parent.winfo_height()

    # 获取子窗口的尺寸
    child_width = child.winfo_reqwidth()
    child_height = child.winfo_reqheight()

    # 计算子窗口在父窗口中居中的位置
    position_x = int(parent_x + (parent_width - child_width) / 2)
    position_y = int(parent_y + (parent_height - child_height) / 2)

    # 设置子窗口的位置
    child.geometry(f"+{position_x}+{position_y}")


class MainApplication(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("主窗口")
        # 隐藏窗体的标题栏和边框
        self.overrideredirect(True)
        # 在这里定义并初始化变量
        self.offset_x = 0  # 例如，初始化为0
        self.offset_y = 0  # 同样初始化为0
        # self.geometry("400x300")
        # 仅设置窗口的启动位置为屏幕左上角外的-1,-1，不设置大小
        offsetSize = '2'
        height = 20
        self.geometry(f'+-{offsetSize}+-{offsetSize}')
        self.screenshot_taker = ScreenshotTaker()

        # 模拟的标题栏
        title_bar = tk.Frame(self, bg='gray', height=height)
        title_bar.pack(side='bottom', fill='x')  # 放在窗口底部
        title_bar.bind('<Button-1>', self.start_drag)  # 鼠标左键按下
        title_bar.bind('<B1-Motion>', self.on_drag)  # 鼠标左键按下并移动

        # 添加红色关闭按钮
        close_button = tk.Button(title_bar, text='X', bg='red', fg='white', command=self.destroy)
        close_button.pack(side='right')

        open_button = tk.Button(title_bar, text='选择监控名称', bg='red', fg='white', command=self.open_popup)
        open_button.pack(side='bottom')

        # self.label = tk.Label(title_bar)
        # self.label.pack(side='left')

        self.canvas = tk.Canvas(self, width=1366, height=768)
        self.canvas.pack()
        self.img_label = tk.Label(self.canvas)
        self.img_label.pack()

    def open_popup(self):
        popup = PopupWindow(self)
        center_window(self, popup)
        self.wait_window(popup)  # 等待弹出窗体关闭
        selected_value = popup.var.get()  # 获取选中的值
        # self.label.config(text=f"选中的窗口：{selected_value}")
        self.start(selected_value)

    def on_drag(self, event):
        # 计算新位置，考虑窗口的宽度和高度

        x = self.winfo_pointerx() - self.offset_x
        y = self.winfo_pointery() - self.offset_y

        # 由于标题栏在底部，我们需要根据标题栏的位置调整窗口的y坐标
        # 这里假设标题栏的高度为20，如果不同，需要相应调整
        title_bar_height = 20
        y -= (self.winfo_height() - title_bar_height)

        self.geometry(f'+{x}+{y}')

    def start_drag(self, event):
        # 记录鼠标点击位置相对于窗口的偏移量
        self.offset_x = event.x
        self.offset_y = event.y

    def update_image(self, canvas, hwnd, image_label):
        img = self.screenshot_taker.take_screenshot(hwnd)  # 获取最新截图
        imgtk = ImageTk.PhotoImage(image=img)
        image_label.config(image=imgtk)
        image_label.image = imgtk  # 防止被垃圾回收
        canvas.after(10, lambda: self.update_image(canvas, hwnd, image_label))  # 每10ms更新一次

    def start(self, title):
        # 使用窗口标题查找父窗口句柄
        parent_handle = win32gui.FindWindow(None, title)
        if parent_handle:
            print(f"父窗口句柄: {parent_handle}")
            # 查找具有特定类名的子窗口
            finder = WindowFinder(class_name='VMware.CGuestScreenshot')
            child_hwnd = finder.find_child_window_by_class_name(parent_handle)
            if child_hwnd:
                print(f"找到的子窗口句柄: {child_hwnd}")
                self.update_image(self.canvas, child_hwnd, self.img_label)
            else:
                self.label.config(text=f"未找到具有指定类名的子窗口")
        else:
            self.label.config(text=f"未找到指定的父窗口")


def get_window_titles():
    titles = []

    def enum_window_callback(hwnd, _):
        if win32gui.IsWindowVisible(hwnd) and win32gui.GetWindowText(hwnd) != "":
            titles.append(win32gui.GetWindowText(hwnd))

    win32gui.EnumWindows(enum_window_callback, None)
    return titles


class PopupWindow(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.title("选择窗口")
        self.geometry("300x100")

        self.var = tk.StringVar()

        self.combobox = ttk.Combobox(self, textvariable=self.var)
        self.combobox['values'] = get_window_titles()
        self.combobox.pack(pady=10)

        tk.Button(self, text="确定", command=self.on_confirm).pack()

    def on_confirm(self):
        selected_value = self.var.get()
        print(f"选中的值：{selected_value}")
        self.destroy()  # 关闭弹出窗体


if __name__ == "__main__":
    app = MainApplication()
    app.mainloop()

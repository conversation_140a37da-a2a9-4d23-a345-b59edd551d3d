if __name__ == "__main__":
    text = "国有企业资产管理系统模块\r\n购置管理\r\n购置申请\r\n查询\r\n暂存\r\n修改\r\n申请\r\n购置审批\r\n审批\r\n日志\r\n附件上传\r\n自动资产建卡\r\n自动账册入账\r\n自动报送资产预算金额\r\n外部系统对接\r\n对接决策系统获取会议纪要\r\n对接财务系统发送资产预算金额\r\n资产管理\r\n资产卡片\r\n新增资产卡片\r\n修改资产卡片\r\n导入资产卡片\r\n确认资产入账\r\n合同信息上传\r\n合同信息关联\r\n预算调整报送\r\n查询预算调整历史记录\r\n维护资产状态\r\n查询到货状态\r\n录入评估记录\r\n外部系统对接\r\n对接OA产生的合同\r\n对接财务系统发送资产预算金额\r\n账册管理\r\n新增资产类别基础信息\r\n修改资产类别基础信息\r\n删除资产类别基础信息\r\n查询资产类别基础信息\r\n管理资产分类\r\n人工入账\r\n查询入账记录\r\n查询人工入账行为记录\r\n人工出账\r\n查询出账记录\r\n查询人工出账行为记录\r\n盘点管理\r\n新增盘点计划\r\n修改盘点计划\r\n删除盘点计划\r\n生效盘点计划\r\n作废盘点计划\r\n查询盘点计划\r\n确认盘点审计\r\n处置盘点审计异常\r\n查询盘点审计历史\r\n确认评估折旧\r\n处置评估折旧异常\r\n查询评估折旧历史\r\n查询盘点报告\r\n导出盘点报告\r\n统计盘点报告\r\n财务查询\r\n查询付款状况\r\n查询预算状况\r\n查询购置金额\r\n查询资产净值\r\n查询资产残值\r\n评估记录管理\r\n录入评估记录\r\n查询评估记录\r\n合同查询\r\n查询合同\r\n资产使用\r\n领用管理\r\n发起领用申请\r\n查询领用申请\r\n导出领用申请\r\n审批领用申请\r\n查询领用申请审批历史记录\r\n上传领用申请合同附件\r\n关联领用申请合同附件\r\n自动变更账册领用信息\r\n外部系统对接\r\n对接OA产生的领用合同\r\n借用管理\r\n发起借用申请\r\n查询借用申请\r\n导出借用申请\r\n审批借用申请\r\n查询借用申请审批历史记录\r\n上传借用申请合同附件\r\n关联借用申请合同附件\r\n自动变更账册借用信息\r\n外部系统对接\r\n对接OA产生的借用合同\r\n归还管理\r\n发起归还申请\r\n查询归还申请\r\n导出归还申请\r\n审批归还申请\r\n查询归还申请审批历史记录\r\n上传归还申请合同附件\r\n关联归还申请合同附件\r\n自动变更账册归还信息\r\n外部系统对接\r\n对接OA产生的归还合同\r\n调拨管理\r\n发起调拨申请\r\n查询调拨申请\r\n导出调拨申请\r\n审批调拨申请\r\n查询调拨申请审批历史记录\r\n上传调拨申请合同附件\r\n关联调拨申请合同附件\r\n自动变更账册调拨信息\r\n外部系统对接\r\n对接OA产生的调拨合同\r\n变动\r\n发起变动申请\r\n查询变动申请\r\n导出变动申请\r\n审批变动申请\r\n查询变动申请审批历史记录\r\n上传变动申请合同附件\r\n关联变动申请合同附件\r\n自动变更账册变动信息\r\n外部系统对接\r\n对接OA产生的变动合同\r\n资产经营\r\n租赁管理\r\n发起租赁申请\r\n查询租赁申请\r\n导出租赁申请\r\n审批租赁申请\r\n查询租赁申请审批历史记录\r\n上传租赁申请合同附件\r\n关联租赁申请合同附件\r\n上传租金底价方案\r\n关联决策系统会议纪要\r\n自动变更账册租赁信息\r\n查询租金信息\r\n预警租金收取风险\r\n外部系统对接\r\n对接OA产生的租赁合同\r\n对接财务系统获取租金收取情况\r\n对接决策系统获取会议纪要\r\n资产处置\r\n转让管理\r\n发起转让申请\r\n查询转让申请\r\n导出转让申请\r\n审批转让申请\r\n查询转让申请审批历史记录\r\n上传转让申请合同附件\r\n关联转让申请合同附件\r\n关联决策系统会议纪要\r\n查询转让资产总价\r\n自动变更账册转让信息\r\n外部系统对接\r\n对接OA产生的转让合同\r\n对接财务系统获取转让资产总价\r\n对接决策系统获取会议纪要\r\n报废管理\r\n发起报废申请\r\n查询报废申请\r\n导出报废申请\r\n审批报废申请\r\n查询报废申请审批历史记录\r\n上传报废申请合同附件\r\n关联报废申请合同附件\r\n关联决策系统会议纪要\r\n查询报废处置费\r\n自动变更账册报废信息\r\n外部系统对接\r\n对接OA产生的报废合同\r\n对接财务系统获取处置费\r\n对接决策系统获取会议纪要\r\n资产信息上报\r\n从资产卡片上报\r\n匹配上报规则\r\n选择上报内容\r\n上报资产信息\r\n查询上报历史记录\r\n上报计划管理\r\n新增上报计划\r\n修改上报计划\r\n删除上报计划\r\n查询上报计划\r\n匹配上报规则\r\n选择上报内容\r\n上报资产信息\r\n查询上报历史记录\r\n上报规则维护\r\n新增规则维护\r\n修改规则维护\r\n删除规则维护\r\n启用规则维护\r\n禁用规则维护\r\n查询规则维护\r\n上报失败记录管理\r\n查询失败记录\r\n重报失败记录\r\n上报回执结果查询\r\n查询上报回执结果\r\n上报API地址管理\r\n新增API地址\r\n修改API地址\r\n删除API地址\r\n启用API地址\r\n禁用API地址\r\n查询API地址\r\n资产信息上报API\r\n获取资产信息的APIs\r\n新增API调用白名单\r\n修改API调用白名单\r\n删除API调用白名单\r\n启用API调用白名单\r\n禁用API调用白名单\r\n查询API调用白名单\r\n查询调用记录\r\n分析调用情况\r\n外部系统对接\r\n国资委上报接口调用\r\n资产分析与预警\r\n统计报表\r\n经营现状\r\n收益分析\r\n闲置情况\r\n租金逾期\r\n租金收益\r\n分析报表\r\n经营现状\r\n收益分析\r\n闲置情况\r\n租金逾期\r\n租金收益\r\n资产地图\r\n资产位置\r\n资产分类\r\n资产价值\r\n资产状态\r\n经营现状\r\n其他分析\r\n按实际需求扩展建设\r\n预警规则管理\r\n新增预警规则\r\n修改预警规则\r\n删除预警规则\r\n启用预警规则\r\n禁用预警规则\r\n查询预警规则\r\n预警功能\r\n租金逾期预警\r\n租赁期限过长预警\r\n租金长期未收取预警\r\n其他预警\r\n按实际需求扩展建设\r\n基础信息管理\r\n工作流管理\r\n新增工作流\r\n修改工作流\r\n删除工作流\r\n查询工作流\r\n关联表单\r\n查询流转记录\r\n用户管理\r\n新增用户\r\n修改用户\r\n删除用户\r\n启用用户\r\n禁用用户\r\n查询用户\r\n权限菜单管理\r\n新增权限菜单\r\n修改权限菜单\r\n删除权限菜单\r\n显示权限菜单\r\n隐藏权限菜单\r\n查询权限菜单\r\n日志管理\r\n查询系统运行日志\r\n查询用户登录日志\r\n查询用户操作日志\r\n外部系统对接\r\n决策系统的会议纪要\r\nOA系统的购置合同\r\nOA请款申请\r\nOA产生的领用合同\r\nOA产生的归还合同\r\nOA产生的调拨合同\r\nOA产生的变动合同\r\nOA产生的租赁合同\r\nOA产生的转让合同\r\nOA产生的报废合同\r\n财务系统获取租金收取情况\r\n财务系统获取转让资产总价\r\n财务系统获取处置费\r\n向财务系统发送预算计划\r\n国资委api\r\n国资委上报接口调用\r\n数据库适配\r\n适配TDSQL\r\n适配OCEANBASE"
    # 将文本分割成行
    lines = text.strip().split('\r\n')

    # 使用字典来跟踪每行出现的次数
    line_counts = {}

    # 遍历每一行，计数
    for line in lines:
        if line in line_counts:
            line_counts[line] += 1
        else:
            line_counts[line] = 1

    # 找出重复的行
    duplicates = {line: count for line, count in line_counts.items() if count > 1}

    print("重复的行及其出现次数:", duplicates)
import cv2
import numpy as np
import mss
import time
import os
from typing import List, Tuple, Optional, Union
import pyautogui
import math

class ImageColorScript:
    """
    完整的图色脚本功能类
    包含区域找图、屏幕找图、区域找色、多点找色、截图、区域截图等功能
    """

    def __init__(self):
        """初始化图色脚本"""
        try:
            self.screen_width, self.screen_height = pyautogui.size()
        except Exception:  # Broad exception to catch display errors like 'DISPLAY' key error
            print("Warning: Failed to get screen size via pyautogui. Using default 1920x1080. This is normal in headless environments.")
            self.screen_width, self.screen_height = 1920, 1080
        # 禁用pyautogui的安全检查
        pyautogui.FAILSAFE = False

    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        截取屏幕图像

        Args:
            region: 截图区域 (x, y, width, height)，None表示全屏

        Returns:
            截取的图像数组 (BGR格式)
        """
        with mss.mss() as sct:
            if region is None:
                # 全屏截图
                monitor = sct.monitors[1]
                img = sct.grab(monitor)
            else:
                # 区域截图
                x, y, width, height = region
                monitor = {"top": y, "left": x, "width": width, "height": height}
                img = sct.grab(monitor)

            img_np = np.array(img)
            img_bgr = cv2.cvtColor(img_np, cv2.COLOR_BGRA2BGR)
            return img_bgr
    
    def save_screenshot(self, filename: str, region: Optional[Tuple[int, int, int, int]] = None) -> bool:
        """
        保存截图到文件
        
        Args:
            filename: 保存的文件名
            region: 截图区域 (x, y, width, height)，None表示全屏
            
        Returns:
            是否保存成功
        """
        try:
            img = self.capture_screen(region)
            cv2.imwrite(filename, img)
            return True
        except Exception as e:
            print(f"保存截图失败: {e}")
            return False
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        获取指定坐标的颜色值
        
        Args:
            x: X坐标
            y: Y坐标
            
        Returns:
            颜色值 (B, G, R)
        """
        img = self.capture_screen(region=(x, y, 1, 1))
        return tuple(img[0, 0])
    
    def color_distance(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """
        计算两个颜色之间的距离
        
        Args:
            color1: 颜色1 (B, G, R)
            color2: 颜色2 (B, G, R)
            
        Returns:
            颜色距离
        """
        return math.sqrt(sum((a - b) ** 2 for a, b in zip(color1, color2)))

    def find_image(self,
                   template_path: str,
                   region: Optional[Tuple[int, int, int, int]] = None,
                   threshold: float = 0.8,
                   max_results: int = 1,
                   transparent_background: bool = False,
                   ignore_color: Optional[Tuple[int, int, int]] = None,
                   scale_range: Optional[Tuple[float, float]] = None,
                   scale_steps: int = 10) -> List[Tuple[int, int, int, int]]:
        """
        在屏幕或指定区域查找图像（增强版）

        Args:
            template_path: 模板图像路径
            region: 搜索区域 (x, y, width, height)，None表示全屏
            threshold: 相似度阈值 (0-1)
            max_results: 最大返回结果数量
            transparent_background: 是否处理模板的透明背景 (需要4通道PNG)
            ignore_color: 要忽略的背景颜色 (B, G, R)，例如紫色 (255, 0, 255)
            scale_range: 缩放范围 (min_scale, max_scale)，例如 (0.8, 1.2)
            scale_steps: 在缩放范围内尝试的步数

        Returns:
            找到的位置列表 [(x, y, width, height), ...]
        """
        # --- 1. 读取模板图像 ---
        # 使用 IMREAD_UNCHANGED 读取图像，以保留Alpha通道
        template = cv2.imread(template_path, cv2.IMREAD_UNCHANGED)
        if template is None:
            raise FileNotFoundError(f"模板文件 {template_path} 不存在")

        template_bgr = template
        mask = None

        # --- 2. 创建掩码 (处理透明背景或指定颜色) ---
        if transparent_background and template.shape[2] == 4:
            # 如果是4通道图像 (BGRA)，提取Alpha通道作为掩码
            print("正在处理透明背景...")
            mask = template[:, :, 3]
            template_bgr = template[:, :, :3]
            # 对掩码进行二值化，确保只有完全透明的区域被忽略
            _, mask = cv2.threshold(mask, 1, 255, cv2.THRESH_BINARY)

        elif ignore_color is not None:
            # 如果指定了要忽略的颜色
            print(f"正在处理指定忽略颜色: {ignore_color}")
            if template.shape[2] == 4:  # 如果是4通道图，先转为3通道
                template_bgr = template[:, :, :3]
            # 创建一个掩码，所有不等于ignore_color的像素都为255（保留）
            mask = cv2.inRange(template_bgr, np.array(ignore_color), np.array(ignore_color))
            mask = cv2.bitwise_not(mask)  # 反转掩码

        # 截取搜索区域
        screen_img = self.capture_screen(region)

        best_match = None
        all_matches = []

        # --- 3. 执行多尺度或单尺度匹配 ---
        if scale_range:
            # --- 多尺度匹配 ---
            print(f"执行多尺度搜索，范围: {scale_range}, 步数: {scale_steps}")
            scales = np.linspace(scale_range[0], scale_range[1], scale_steps)[::-1]

            for scale in scales:
                h, w = template_bgr.shape[:2]
                # 缩放模板和掩码
                resized_h, resized_w = int(h * scale), int(w * scale)
                if resized_h == 0 or resized_w == 0:
                    continue

                resized_template = cv2.resize(template_bgr, (resized_w, resized_h), interpolation=cv2.INTER_AREA)

                # 如果屏幕图像比缩放后的模板还小，则跳过
                if resized_h > screen_img.shape[0] or resized_w > screen_img.shape[1]:
                    continue

                resized_mask = None
                if mask is not None:
                    resized_mask = cv2.resize(mask, (resized_w, resized_h), interpolation=cv2.INTER_AREA)

                # 执行模板匹配
                result = cv2.matchTemplate(screen_img, resized_template, cv2.TM_CCOEFF_NORMED, mask=resized_mask)

                # 查找当前尺度的最佳匹配
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                if max_val >= threshold:
                    # 如果找到了一个足够好的匹配，记录下来
                    # 使用一个元组 (分数, 位置, 尺寸) 来存储
                    all_matches.append((max_val, max_loc, (resized_w, resized_h)))

        else:
            # --- 单尺度匹配 ---
            h, w = template_bgr.shape[:2]
            if h > screen_img.shape[0] or w > screen_img.shape[1]:
                print("模板比截图区域大，无法匹配。")
                return []

            result = cv2.matchTemplate(screen_img, template_bgr, cv2.TM_CCOEFF_NORMED, mask=mask)

            # 查找所有匹配位置
            locations = np.where(result >= threshold)
            for pt in zip(*locations[::-1]):
                all_matches.append((result[pt[1], pt[0]], pt, (w, h)))

        if not all_matches:
            return []

        # --- 4. 处理和过滤结果 ---
        # 按相似度从高到低排序
        all_matches.sort(key=lambda x: x[0], reverse=True)

        final_results = []

        # 将匹配结果转换为 (x, y, w, h) 格式，并应用非极大值抑制
        raw_boxes = []
        for score, pos, size in all_matches:
            x, y = pos
            w, h = size
            # 如果指定了搜索区域，需要转换为屏幕坐标
            if region is not None:
                x += region[0]
                y += region[1]
            raw_boxes.append((x, y, w, h))

        # 去重
        if len(raw_boxes) > 0:
            final_boxes = self._non_max_suppression(raw_boxes)
            final_results = final_boxes

        # 限制返回结果数量
        return final_results[:max_results]

    def find_image_center(self,
                          template_path: str,
                          region: Optional[Tuple[int, int, int, int]] = None,
                          threshold: float = 0.8,
                          **kwargs) -> Optional[Tuple[int, int]]:
        """
        查找图像并返回中心坐标 (增强版)

        Args:
            template_path: 模板图像路径
            region: 搜索区域 (x, y, width, height)，None表示全屏
            threshold: 相似度阈值 (0-1)
            **kwargs: find_image的其他参数 (transparent_background, ignore_color, scale_range)

        Returns:
            图像中心坐标 (x, y)，未找到返回None
        """
        results = self.find_image(template_path, region, threshold, max_results=1, **kwargs)
        if results:
            x, y, w, h = results[0]
            return (x + w // 2, y + h // 2)
        return None

    def _non_max_suppression(self, boxes: List[Tuple[int, int, int, int]],
                             overlap_threshold: float = 0.3) -> List[Tuple[int, int, int, int]]:
        """
        非极大值抑制去重

        Args:
            boxes: 边界框列表 [(x, y, w, h), ...]
            overlap_threshold: 重叠阈值

        Returns:
            去重后的边界框列表
        """
        if len(boxes) == 0:
            return []

        # 将(x, y, w, h)转换为(x1, y1, x2, y2)
        np_boxes = np.array([[b[0], b[1], b[0] + b[2], b[1] + b[3]] for b in boxes])

        pick = []
        x1 = np_boxes[:, 0]
        y1 = np_boxes[:, 1]
        x2 = np_boxes[:, 2]
        y2 = np_boxes[:, 3]

        area = (x2 - x1 + 1) * (y2 - y1 + 1)
        # 使用 y2 坐标进行排序
        idxs = np.argsort(y2)

        while len(idxs) > 0:
            last = len(idxs) - 1
            i = idxs[last]
            pick.append(i)

            xx1 = np.maximum(x1[i], x1[idxs[:last]])
            yy1 = np.maximum(y1[i], y1[idxs[:last]])
            xx2 = np.minimum(x2[i], x2[idxs[:last]])
            yy2 = np.minimum(y2[i], y2[idxs[:last]])

            w = np.maximum(0, xx2 - xx1 + 1)
            h = np.maximum(0, yy2 - yy1 + 1)

            # 计算重叠率 (IoU - Intersection over Union)
            overlap = (w * h) / area[idxs[:last]]

            # 删除重叠率大于阈值的边界框
            idxs = np.delete(idxs, np.concatenate(([last], np.where(overlap > overlap_threshold)[0])))

        # 返回原始格式的边界框
        return [boxes[i] for i in pick]

    def find_color(self, target_color: Tuple[int, int, int], region: Optional[Tuple[int, int, int, int]] = None,
                   tolerance: int = 10, max_results: int = 1) -> List[Tuple[int, int]]:
        """
        在屏幕或指定区域查找颜色
        
        Args:
            target_color: 目标颜色 (B, G, R)
            region: 搜索区域 (x, y, width, height)，None表示全屏
            tolerance: 颜色容差
            max_results: 最大返回结果数量
            
        Returns:
            找到的坐标列表 [(x, y), ...]
        """
        # 截取搜索区域
        img = self.capture_screen(region)
        
        # 创建颜色掩码
        lower_bound = np.array([max(0, c - tolerance) for c in target_color])
        upper_bound = np.array([min(255, c + tolerance) for c in target_color])
        
        mask = cv2.inRange(img, lower_bound, upper_bound)
        
        # 查找匹配的像素点
        y_coords, x_coords = np.where(mask == 255)
        
        # 转换为坐标列表
        matches = []
        for x, y in zip(x_coords, y_coords):
            # 如果指定了搜索区域，需要转换为屏幕坐标
            if region is not None:
                x += region[0]
                y += region[1]
            matches.append((int(x), int(y)))
        
        # 限制返回结果数量
        return matches[:max_results]
    
    def find_color_center(self, target_color: Tuple[int, int, int], 
                         region: Optional[Tuple[int, int, int, int]] = None,
                         tolerance: int = 10) -> Optional[Tuple[int, int]]:
        """
        查找颜色并返回第一个匹配点的坐标
        
        Args:
            target_color: 目标颜色 (B, G, R)
            region: 搜索区域 (x, y, width, height)，None表示全屏
            tolerance: 颜色容差
            
        Returns:
            第一个匹配点的坐标 (x, y)，未找到返回None
        """
        results = self.find_color(target_color, region, tolerance, max_results=1)
        return results[0] if results else None

    def find_multi_color(self, color_points: List[Tuple[int, int, Tuple[int, int, int]]],
                        region: Optional[Tuple[int, int, int, int]] = None,
                        tolerance: int = 10) -> Optional[Tuple[int, int]]:
        """
        多点找色

        Args:
            color_points: 颜色点列表 [(相对x, 相对y, (B, G, R)), ...]
                         第一个点为基准点，其他点为相对于基准点的偏移
            region: 搜索区域 (x, y, width, height)，None表示全屏
            tolerance: 颜色容差

        Returns:
            基准点的坐标 (x, y)，未找到返回None
        """
        if not color_points:
            return None

        # 第一个点作为基准点
        base_offset_x, base_offset_y, base_color = color_points[0]

        # 查找基准点的所有可能位置
        base_matches = self.find_color(base_color, region, tolerance, max_results=1000)

        # 检查每个基准点位置
        for base_x, base_y in base_matches:
            all_match = True

            # 检查其他所有点
            for offset_x, offset_y, target_color in color_points[1:]:
                check_x = base_x + offset_x - base_offset_x
                check_y = base_y + offset_y - base_offset_y

                # 检查坐标是否在有效范围内
                if (check_x < 0 or check_y < 0 or
                    check_x >= self.screen_width or check_y >= self.screen_height):
                    all_match = False
                    break

                # 获取该位置的颜色
                actual_color = self.get_pixel_color(check_x, check_y)

                # 检查颜色是否匹配
                if self.color_distance(actual_color, target_color) > tolerance * math.sqrt(3):
                    all_match = False
                    break

            if all_match:
                return (base_x, base_y)

        return None

    # ==================== 鼠标键盘操作 ====================

    def click(self, x: int, y: int, button: str = 'left', clicks: int = 1, interval: float = 0.1):
        """
        鼠标点击

        Args:
            x: X坐标
            y: Y坐标
            button: 鼠标按键 ('left', 'right', 'middle')
            clicks: 点击次数
            interval: 点击间隔
        """
        pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)

    def double_click(self, x: int, y: int):
        """双击"""
        pyautogui.doubleClick(x, y)

    def right_click(self, x: int, y: int):
        """右键点击"""
        pyautogui.rightClick(x, y)

    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 1.0):
        """
        拖拽操作

        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 拖拽持续时间
        """
        pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration)

    def scroll(self, x: int, y: int, clicks: int):
        """
        滚轮操作

        Args:
            x: X坐标
            y: Y坐标
            clicks: 滚动次数，正数向上，负数向下
        """
        pyautogui.scroll(clicks, x=x, y=y)

    def type_text(self, text: str, interval: float = 0.1):
        """
        输入文本

        Args:
            text: 要输入的文本
            interval: 字符间隔
        """
        pyautogui.typewrite(text, interval=interval)

    def press_key(self, key: str):
        """
        按键

        Args:
            key: 按键名称 (如 'enter', 'space', 'ctrl', 'alt' 等)
        """
        pyautogui.press(key)

    def key_combination(self, *keys):
        """
        组合键

        Args:
            keys: 按键组合 (如 'ctrl', 'c')
        """
        pyautogui.hotkey(*keys)

    # ==================== 图像处理和比较 ====================

    def compare_images(self, img1_path: str, img2_path: str, method: str = 'histogram') -> float:
        """
        比较两张图片的相似度

        Args:
            img1_path: 图片1路径
            img2_path: 图片2路径
            method: 比较方法 ('histogram', 'mse', 'ssim', 'template')

        Returns:
            相似度 (0-1)，1表示完全相同
        """
        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)

        if img1 is None or img2 is None:
            return 0.0

        if method == 'histogram':
            return self._compare_histogram(img1, img2)
        elif method == 'mse':
            return self._compare_mse(img1, img2)
        elif method == 'ssim':
            return self._compare_ssim(img1, img2)
        elif method == 'template':
            return self._compare_template(img1, img2)
        else:
            return self._compare_histogram(img1, img2)

    def _compare_histogram(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用直方图比较图像相似度"""
        # 转换为HSV颜色空间
        hsv1 = cv2.cvtColor(img1, cv2.COLOR_BGR2HSV)
        hsv2 = cv2.cvtColor(img2, cv2.COLOR_BGR2HSV)

        # 计算直方图
        hist1 = cv2.calcHist([hsv1], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])
        hist2 = cv2.calcHist([hsv2], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])

        # 使用相关性比较
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        return max(0.0, correlation)  # 确保返回值在0-1之间

    def _compare_mse(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用均方误差比较图像相似度"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 计算均方误差
        mse = np.mean((img1.astype(float) - img2.astype(float)) ** 2)

        # 转换为相似度 (MSE越小，相似度越高)
        max_mse = 255.0 ** 2  # 最大可能的MSE
        similarity = 1.0 - (mse / max_mse)
        return max(0.0, similarity)

    def _compare_ssim(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用结构相似性指数比较图像"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 计算SSIM (简化版本)
        mu1 = cv2.GaussianBlur(gray1.astype(float), (11, 11), 1.5)
        mu2 = cv2.GaussianBlur(gray2.astype(float), (11, 11), 1.5)

        mu1_sq = mu1 * mu1
        mu2_sq = mu2 * mu2
        mu1_mu2 = mu1 * mu2

        sigma1_sq = cv2.GaussianBlur(gray1.astype(float) * gray1.astype(float), (11, 11), 1.5) - mu1_sq
        sigma2_sq = cv2.GaussianBlur(gray2.astype(float) * gray2.astype(float), (11, 11), 1.5) - mu2_sq
        sigma12 = cv2.GaussianBlur(gray1.astype(float) * gray2.astype(float), (11, 11), 1.5) - mu1_mu2

        c1 = (0.01 * 255) ** 2
        c2 = (0.03 * 255) ** 2

        ssim_map = ((2 * mu1_mu2 + c1) * (2 * sigma12 + c2)) / ((mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2))
        return float(np.mean(ssim_map))

    def _compare_template(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用模板匹配比较图像相似度"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 使用模板匹配
        result = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)
        return max(0.0, float(np.max(result)))  # 确保返回值非负

    def _create_mask_for_background(self, template: np.ndarray, background_color: tuple = (255, 0, 255), tolerance: int = 30) -> np.ndarray:
        """
        为模板创建掩码，忽略指定的背景色

        Args:
            template: 模板图像
            background_color: 背景色 (B, G, R)，默认为紫色
            tolerance: 颜色容差

        Returns:
            掩码图像（255=前景，0=背景）
        """
        # 创建颜色范围
        lower_bound = np.array([max(0, c - tolerance) for c in background_color])
        upper_bound = np.array([min(255, c + tolerance) for c in background_color])

        # 创建掩码（背景为0，前景为255）
        mask = cv2.inRange(template, lower_bound, upper_bound)
        mask = 255 - mask  # 反转掩码，使前景为255，背景为0

        return mask

    def find_image_in_image(self, large_img_path: str, small_img_path: str, threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        在大图中查找小图的位置（专门用于区域图在全屏图中的定位）

        Args:
            large_img_path: 大图路径
            small_img_path: 小图路径
            threshold: 相似度阈值

        Returns:
            小图在大图中的位置 (x, y)，未找到返回None
        """
        large_img = cv2.imread(large_img_path)
        small_img = cv2.imread(small_img_path)

        if large_img is None or small_img is None:
            return None

        # 转换为灰度图
        large_gray = cv2.cvtColor(large_img, cv2.COLOR_BGR2GRAY)
        small_gray = cv2.cvtColor(small_img, cv2.COLOR_BGR2GRAY)

        # 模板匹配
        result = cv2.matchTemplate(large_gray, small_gray, cv2.TM_CCOEFF_NORMED)

        # 查找最佳匹配位置
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        if max_val >= threshold:
            return max_loc
        else:
            return None

    def wait_for_image(self, template_path: str, timeout: float = 10.0,
                      region: Optional[Tuple[int, int, int, int]] = None,
                      threshold: float = 0.8, check_interval: float = 0.5) -> Optional[Tuple[int, int]]:
        """
        等待图像出现

        Args:
            template_path: 模板图像路径
            timeout: 超时时间（秒）
            region: 搜索区域
            threshold: 相似度阈值
            check_interval: 检查间隔

        Returns:
            图像中心坐标，超时返回None
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            result = self.find_image_center(template_path, region, threshold)
            if result:
                return result
            time.sleep(check_interval)

        return None

    def wait_for_color(self, target_color: Tuple[int, int, int], timeout: float = 10.0,
                      region: Optional[Tuple[int, int, int, int]] = None,
                      tolerance: int = 10, check_interval: float = 0.5) -> Optional[Tuple[int, int]]:
        """
        等待颜色出现

        Args:
            target_color: 目标颜色
            timeout: 超时时间（秒）
            region: 搜索区域
            tolerance: 颜色容差
            check_interval: 检查间隔

        Returns:
            颜色坐标，超时返回None
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            result = self.find_color_center(target_color, region, tolerance)
            if result:
                return result
            time.sleep(check_interval)

        return None

    def find_similar_regions(self, large_img_path: str, template_path: str,
                           method: str = 'template', threshold: float = 0.8,
                           max_results: int = 10) -> List[Tuple[int, int, float]]:
        """
        在大图中查找与模板相似的所有区域

        Args:
            large_img_path: 大图路径
            template_path: 模板图像路径
            method: 比较方法 ('template', 'histogram', 'mse', 'ssim')
            threshold: 相似度阈值 (0-1)
            max_results: 最大返回结果数量

        Returns:
            相似区域列表 [(x, y, similarity), ...]
        """
        large_img = cv2.imread(large_img_path)
        template = cv2.imread(template_path)

        if large_img is None or template is None:
            return []

        h, w = template.shape[:2]
        large_h, large_w = large_img.shape[:2]

        results = []

        if method == 'template':
            # 使用模板匹配（最快的方法）
            gray_large = cv2.cvtColor(large_img, cv2.COLOR_BGR2GRAY)
            gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

            result = cv2.matchTemplate(gray_large, gray_template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(result >= threshold)

            for pt in zip(*locations[::-1]):
                x, y = pt
                similarity = float(result[y, x])
                results.append((x, y, similarity))

        else:
            # 使用优化的搜索策略
            if method == 'mse':
                # MSE方法使用粗搜索+精搜索策略
                results = self._fast_mse_search(large_img, template, threshold)
            elif method == 'histogram':
                # 直方图方法使用中等步长
                step_size = max(2, min(w, h) // 8)
                results = self._sliding_window_search(large_img, template, method, threshold, step_size)
            elif method == 'ssim':
                # SSIM方法使用较大步长
                step_size = max(3, min(w, h) // 6)
                results = self._sliding_window_search(large_img, template, method, threshold, step_size)
            else:
                # 默认使用直方图方法
                step_size = max(2, min(w, h) // 8)
                results = self._sliding_window_search(large_img, template, 'histogram', threshold, step_size)

        # 按相似度排序
        results.sort(key=lambda x: x[2], reverse=True)

        # 非极大值抑制去重
        if len(results) > 1:
            results = self._nms_similarity_results(results, w, h)

        return results[:max_results]

    def _nms_similarity_results(self, results: List[Tuple[int, int, float]],
                               template_w: int, template_h: int,
                               overlap_threshold: float = 0.3) -> List[Tuple[int, int, float]]:
        """
        对相似度结果进行非极大值抑制

        Args:
            results: 结果列表 [(x, y, similarity), ...]
            template_w: 模板宽度
            template_h: 模板高度
            overlap_threshold: 重叠阈值

        Returns:
            去重后的结果列表
        """
        if len(results) == 0:
            return []

        # 转换为边界框格式
        boxes = []
        scores = []
        for x, y, similarity in results:
            boxes.append([x, y, x + template_w, y + template_h])
            scores.append(similarity)

        boxes = np.array(boxes)
        scores = np.array(scores)

        # 按分数排序
        indices = np.argsort(scores)[::-1]

        keep = []
        while len(indices) > 0:
            # 选择分数最高的
            current = indices[0]
            keep.append(current)

            if len(indices) == 1:
                break

            # 计算与其他框的重叠
            current_box = boxes[current]
            other_boxes = boxes[indices[1:]]

            # 计算交集
            x1 = np.maximum(current_box[0], other_boxes[:, 0])
            y1 = np.maximum(current_box[1], other_boxes[:, 1])
            x2 = np.minimum(current_box[2], other_boxes[:, 2])
            y2 = np.minimum(current_box[3], other_boxes[:, 3])

            # 计算交集面积
            intersection = np.maximum(0, x2 - x1) * np.maximum(0, y2 - y1)

            # 计算并集面积
            current_area = (current_box[2] - current_box[0]) * (current_box[3] - current_box[1])
            other_areas = (other_boxes[:, 2] - other_boxes[:, 0]) * (other_boxes[:, 3] - other_boxes[:, 1])
            union = current_area + other_areas - intersection

            # 计算IoU
            iou = intersection / union

            # 保留IoU小于阈值的框
            indices = indices[1:][iou <= overlap_threshold]

        # 返回保留的结果
        return [results[i] for i in keep]

    def find_best_match_region(self, large_img_path: str, template_path: str,
                              method: str = 'template') -> Optional[Tuple[int, int, float]]:
        """
        在大图中查找最佳匹配区域

        Args:
            large_img_path: 大图路径
            template_path: 模板图像路径
            method: 比较方法

        Returns:
            最佳匹配位置和相似度 (x, y, similarity)，未找到返回None
        """
        results = self.find_similar_regions(large_img_path, template_path, method,
                                          threshold=0.0, max_results=1)
        return results[0] if results else None

    def find_all_matches_above_threshold(self, large_img_path: str, template_path: str,
                                       method: str = 'template', threshold: float = 0.8,
                                       max_results: int = 50) -> List[Tuple[int, int, float]]:
        """
        查找所有超过阈值的匹配区域

        Args:
            large_img_path: 大图路径
            template_path: 模板图像路径
            method: 比较方法
            threshold: 相似度阈值
            max_results: 最大结果数量

        Returns:
            匹配区域列表 [(x, y, similarity), ...]
        """
        return self.find_similar_regions(large_img_path, template_path, method,
                                       threshold, max_results)

    def _fast_mse_search(self, large_img: np.ndarray, template: np.ndarray,
                        threshold: float) -> List[Tuple[int, int, float]]:
        """
        快速MSE搜索：使用粗搜索+精搜索策略

        Args:
            large_img: 大图
            template: 模板图像
            threshold: 阈值

        Returns:
            匹配结果列表
        """
        h, w = template.shape[:2]
        large_h, large_w = large_img.shape[:2]
        results = []

        # 第一阶段：粗搜索（大步长，低精度）
        coarse_step = max(8, min(w, h) // 4)
        coarse_candidates = []

        # 使用缩小的图像进行粗搜索
        scale_factor = 0.5
        small_large = cv2.resize(large_img, None, fx=scale_factor, fy=scale_factor)
        small_template = cv2.resize(template, None, fx=scale_factor, fy=scale_factor)
        small_h, small_w = small_template.shape[:2]

        for y in range(0, small_large.shape[0] - small_h + 1, coarse_step):
            for x in range(0, small_large.shape[1] - small_w + 1, coarse_step):
                region = small_large[y:y+small_h, x:x+small_w]
                similarity = self._compare_mse(region, small_template)

                # 降低粗搜索的阈值
                if similarity >= threshold * 0.7:
                    # 转换回原始坐标
                    orig_x = int(x / scale_factor)
                    orig_y = int(y / scale_factor)
                    coarse_candidates.append((orig_x, orig_y, similarity))

        # 第二阶段：精搜索（小步长，高精度）
        fine_step = 2
        for coarse_x, coarse_y, _ in coarse_candidates:
            # 在粗搜索结果周围进行精搜索
            search_range = coarse_step * 2
            start_x = max(0, coarse_x - search_range)
            end_x = min(large_w - w + 1, coarse_x + search_range)
            start_y = max(0, coarse_y - search_range)
            end_y = min(large_h - h + 1, coarse_y + search_range)

            for y in range(start_y, end_y, fine_step):
                for x in range(start_x, end_x, fine_step):
                    region = large_img[y:y+h, x:x+w]
                    similarity = self._compare_mse(region, template)

                    if similarity >= threshold:
                        results.append((x, y, similarity))

        return results

    def _sliding_window_search(self, large_img: np.ndarray, template: np.ndarray,
                              method: str, threshold: float, step_size: int) -> List[Tuple[int, int, float]]:
        """
        滑动窗口搜索

        Args:
            large_img: 大图
            template: 模板图像
            method: 比较方法
            threshold: 阈值
            step_size: 步长

        Returns:
            匹配结果列表
        """
        h, w = template.shape[:2]
        large_h, large_w = large_img.shape[:2]
        results = []

        for y in range(0, large_h - h + 1, step_size):
            for x in range(0, large_w - w + 1, step_size):
                region = large_img[y:y+h, x:x+w]

                if method == 'histogram':
                    similarity = self._compare_histogram(region, template)
                elif method == 'ssim':
                    similarity = self._compare_ssim(region, template)
                else:
                    similarity = self._compare_histogram(region, template)

                if similarity >= threshold:
                    results.append((x, y, similarity))

        return results

    def find_similar_regions_fast(self, large_img_path: str, template_path: str,
                                 method: str = 'template', threshold: float = 0.8,
                                 max_results: int = 10, speed_mode: str = 'balanced') -> List[Tuple[int, int, float]]:
        """
        快速相似区域搜索（优化版本）

        Args:
            large_img_path: 大图路径
            template_path: 模板图像路径
            method: 比较方法
            threshold: 相似度阈值
            max_results: 最大结果数
            speed_mode: 速度模式 ('fast', 'balanced', 'accurate')

        Returns:
            匹配结果列表
        """
        large_img = cv2.imread(large_img_path)
        template = cv2.imread(template_path)

        if large_img is None or template is None:
            return []

        h, w = template.shape[:2]

        # 根据速度模式调整参数
        if speed_mode == 'fast':
            step_multiplier = 8
            scale_factor = 0.3
        elif speed_mode == 'balanced':
            step_multiplier = 4
            scale_factor = 0.5
        else:  # accurate
            step_multiplier = 2
            scale_factor = 0.7

        results = []

        if method == 'template':
            # 模板匹配始终是最快的
            gray_large = cv2.cvtColor(large_img, cv2.COLOR_BGR2GRAY)
            gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

            result = cv2.matchTemplate(gray_large, gray_template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(result >= threshold)

            for pt in zip(*locations[::-1]):
                x, y = pt
                similarity = float(result[y, x])
                results.append((x, y, similarity))

        elif method == 'mse':
            # 根据速度模式选择搜索策略
            if speed_mode == 'fast':
                # 快速模式：使用单尺度优化搜索
                max_candidates = 20
                results = self._optimized_mse_search(large_img, template, threshold, max_candidates)
            elif speed_mode == 'balanced':
                # 平衡模式：使用优化的多尺度搜索
                results = self._multi_scale_mse_search(large_img, template, threshold, scale_factor)
            else:  # accurate
                # 精确模式：使用更多候选和更小步长
                max_candidates = 100
                results = self._optimized_mse_search(large_img, template, threshold, max_candidates)

        else:
            # 其他方法使用优化的步长
            step_size = max(1, min(w, h) // step_multiplier)
            results = self._sliding_window_search(large_img, template, method, threshold, step_size)

        # 排序和去重
        if len(results) > 1:
            results.sort(key=lambda x: x[2], reverse=True)
            results = self._nms_similarity_results(results, w, h)

        return results[:max_results]

    def _multi_scale_mse_search(self, large_img: np.ndarray, template: np.ndarray,
                               threshold: float, scale_factor: float) -> List[Tuple[int, int, float]]:
        """
        多尺度MSE搜索（优化版）

        Args:
            large_img: 大图
            template: 模板图像
            threshold: 阈值
            scale_factor: 缩放因子

        Returns:
            匹配结果列表
        """
        h, w = template.shape[:2]

        # 第一阶段：粗搜索（缩小图像）
        scaled_large = cv2.resize(large_img, None, fx=scale_factor, fy=scale_factor)
        scaled_template = cv2.resize(template, None, fx=scale_factor, fy=scale_factor)
        scaled_h, scaled_w = scaled_template.shape[:2]

        # 使用较大步长和更严格的阈值
        step_size = max(6, min(scaled_w, scaled_h) // 6)
        coarse_threshold = threshold * 0.85  # 提高粗搜索阈值

        coarse_candidates = []
        for y in range(0, scaled_large.shape[0] - scaled_h + 1, step_size):
            for x in range(0, scaled_large.shape[1] - scaled_w + 1, step_size):
                region = scaled_large[y:y+scaled_h, x:x+scaled_w]
                similarity = self._compare_mse(region, scaled_template)

                if similarity >= coarse_threshold:
                    # 转换回原始坐标
                    orig_x = int(x / scale_factor)
                    orig_y = int(y / scale_factor)
                    coarse_candidates.append((orig_x, orig_y, similarity))

        # 限制候选数量，按相似度排序并取前N个
        max_candidates = 50  # 限制最大候选数量
        if len(coarse_candidates) > max_candidates:
            coarse_candidates.sort(key=lambda x: x[2], reverse=True)
            coarse_candidates = coarse_candidates[:max_candidates]
            print(f"粗搜索找到 {len(coarse_candidates)} 个候选（已限制为前{max_candidates}个）")

        # 第二阶段：精搜索（原始尺寸）
        refined_results = []
        search_range = max(8, min(w, h) // 4)  # 动态调整搜索范围

        for cand_x, cand_y, coarse_sim in coarse_candidates:
            # 在候选位置周围精确搜索
            start_x = max(0, cand_x - search_range)
            end_x = min(large_img.shape[1] - w + 1, cand_x + search_range)
            start_y = max(0, cand_y - search_range)
            end_y = min(large_img.shape[0] - h + 1, cand_y + search_range)

            best_similarity = 0
            best_pos = None

            # 在候选区域内寻找最佳匹配
            step = 2  # 精搜索步长
            for y in range(start_y, end_y, step):
                for x in range(start_x, end_x, step):
                    region = large_img[y:y+h, x:x+w]
                    similarity = self._compare_mse(region, template)

                    if similarity >= threshold and similarity > best_similarity:
                        best_similarity = similarity
                        best_pos = (x, y)

            # 只保留最佳匹配
            if best_pos:
                refined_results.append((best_pos[0], best_pos[1], best_similarity))

        return refined_results

    def _optimized_mse_search(self, large_img: np.ndarray, template: np.ndarray,
                             threshold: float, max_candidates: int = 30) -> List[Tuple[int, int, float]]:
        """
        优化的MSE搜索（单尺度，高效版）

        Args:
            large_img: 大图
            template: 模板图像
            threshold: 阈值
            max_candidates: 最大候选数量

        Returns:
            匹配结果列表
        """
        h, w = template.shape[:2]
        large_h, large_w = large_img.shape[:2]

        # 自适应步长：根据图像大小和模板大小调整
        base_step = max(4, min(w, h) // 8)

        # 如果图像很大，增加步长
        if large_w * large_h > 1920 * 1080:  # 大于1080p
            base_step *= 2

        candidates = []

        # 粗搜索阶段
        for y in range(0, large_h - h + 1, base_step):
            for x in range(0, large_w - w + 1, base_step):
                region = large_img[y:y+h, x:x+w]
                similarity = self._compare_mse(region, template)

                if similarity >= threshold * 0.9:  # 使用较高的粗搜索阈值
                    candidates.append((x, y, similarity))

                # 早期停止：如果找到足够多的高质量候选
                if len(candidates) >= max_candidates * 3:
                    break

            if len(candidates) >= max_candidates * 3:
                break

        # 限制候选数量
        if len(candidates) > max_candidates:
            candidates.sort(key=lambda x: x[2], reverse=True)
            candidates = candidates[:max_candidates]

        # 精搜索阶段：在候选位置周围进行精确搜索
        final_results = []
        search_radius = base_step

        for cand_x, cand_y, _ in candidates:
            best_similarity = 0
            best_pos = None

            # 在候选位置周围搜索
            for dy in range(-search_radius, search_radius + 1, 2):
                for dx in range(-search_radius, search_radius + 1, 2):
                    new_x = cand_x + dx
                    new_y = cand_y + dy

                    if (0 <= new_x <= large_w - w and 0 <= new_y <= large_h - h):
                        region = large_img[new_y:new_y+h, new_x:new_x+w]
                        similarity = self._compare_mse(region, template)

                        if similarity >= threshold and similarity > best_similarity:
                            best_similarity = similarity
                            best_pos = (new_x, new_y)

            if best_pos:
                final_results.append((best_pos[0], best_pos[1], best_similarity))

        return final_results

    def find_best_match_hybrid(self, large_img_input: Union[str, np.ndarray], template_input: Union[str, np.ndarray],
                              template_threshold: float = 0.7, mse_threshold: float = 0.9,
                              max_template_results: int = 20) -> Optional[Tuple[int, int, float]]:
        """
        混合搜索：template快速定位 + MSE精确验证

        Args:
            large_img_input: 大图（文件路径或numpy数组）
            template_input: 模板图像（文件路径或numpy数组）
            template_threshold: template方法的阈值（较低，用于快速筛选）
            mse_threshold: MSE方法的阈值（较高，用于精确验证）
            max_template_results: template方法最大结果数

        Returns:
            最佳匹配位置和MSE相似度 (x, y, mse_similarity)，未找到返回None
        """
        # 处理输入：支持文件路径或numpy数组
        if isinstance(large_img_input, str):
            large_img = cv2.imread(large_img_input)
        else:
            large_img = large_img_input

        if isinstance(template_input, str):
            template = cv2.imread(template_input)
        else:
            template = template_input

        if large_img is None or template is None:
            return None

        h, w = template.shape[:2]

        # 第一阶段：使用template方法快速找到候选位置
        print(f"🔍 第一阶段：template快速搜索（阈值{template_threshold}）...")

        gray_large = cv2.cvtColor(large_img, cv2.COLOR_BGR2GRAY)
        gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

        result = cv2.matchTemplate(gray_large, gray_template, cv2.TM_CCOEFF_NORMED)
        locations = np.where(result >= template_threshold)

        # 获取template候选位置
        template_candidates = []
        for pt in zip(*locations[::-1]):
            x, y = pt
            template_similarity = float(result[y, x])
            template_candidates.append((x, y, template_similarity))

        # 按template相似度排序，取前N个
        template_candidates.sort(key=lambda x: x[2], reverse=True)
        template_candidates = template_candidates[:max_template_results]

        print(f"   找到 {len(template_candidates)} 个template候选")

        if not template_candidates:
            print("   ❌ template方法未找到任何候选")
            return None

        # 第二阶段：对每个候选位置使用MSE精确验证
        print(f"🎯 第二阶段：MSE精确验证（阈值{mse_threshold}）...")

        best_match = None
        best_mse_similarity = 0

        for i, (cand_x, cand_y, template_sim) in enumerate(template_candidates):
            # 提取候选区域
            region = large_img[cand_y:cand_y+h, cand_x:cand_x+w]

            # 检查区域大小是否正确
            if region.shape[:2] != (h, w):
                continue

            # 使用MSE方法计算精确相似度
            mse_similarity = self._compare_mse(region, template)

            print(f"   候选{i+1}: 位置({cand_x}, {cand_y}) template={template_sim:.3f} mse={mse_similarity:.3f}")

            # 更新最佳匹配
            if mse_similarity >= mse_threshold and mse_similarity > best_mse_similarity:
                best_mse_similarity = mse_similarity
                best_match = (cand_x, cand_y, mse_similarity)

                # 🎯 完全匹配优化：MSE=1.0表示完全匹配，无需继续搜索
                if mse_similarity >= 0.999:  # 使用0.999避免浮点精度问题
                    print(f"   🎉 发现完全匹配！MSE={mse_similarity:.3f}，停止搜索")
                    break

        if best_match:
            x, y, similarity = best_match
            print(f"   ✅ 最佳匹配: 位置({x}, {y}) MSE相似度={similarity:.3f}")
        else:
            print(f"   ❌ 没有候选通过MSE验证（阈值{mse_threshold}）")

        return best_match

    def find_all_matches_hybrid(self, large_img_input: Union[str, np.ndarray], template_input: Union[str, np.ndarray],
                               template_threshold: float = 0.7, mse_threshold: float = 0.9,
                               max_template_results: int = 50, max_final_results: int = 10) -> List[Tuple[int, int, float]]:
        """
        混合搜索：template快速定位 + MSE精确验证（返回所有匹配）

        Args:
            large_img_input: 大图（文件路径或numpy数组）
            template_input: 模板图像（文件路径或numpy数组）
            template_threshold: template方法的阈值
            mse_threshold: MSE方法的阈值
            max_template_results: template方法最大结果数
            max_final_results: 最终返回的最大结果数

        Returns:
            所有通过验证的匹配 [(x, y, mse_similarity), ...]
        """
        # 处理输入：支持文件路径或numpy数组
        if isinstance(large_img_input, str):
            large_img = cv2.imread(large_img_input)
        else:
            large_img = large_img_input

        if isinstance(template_input, str):
            template = cv2.imread(template_input)
        else:
            template = template_input

        if large_img is None or template is None:
            return []

        h, w = template.shape[:2]

        # 第一阶段：template快速搜索
        print(f"🔍 第一阶段：template快速搜索...")

        gray_large = cv2.cvtColor(large_img, cv2.COLOR_BGR2GRAY)
        gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

        result = cv2.matchTemplate(gray_large, gray_template, cv2.TM_CCOEFF_NORMED)
        locations = np.where(result >= template_threshold)

        # 获取候选位置并去重
        template_candidates = []
        for pt in zip(*locations[::-1]):
            x, y = pt
            template_similarity = float(result[y, x])
            template_candidates.append((x, y, template_similarity))

        # 非极大值抑制去重
        if len(template_candidates) > 1:
            template_boxes = [(x, y, w, h) for x, y, _ in template_candidates]
            kept_indices = []
            nms_boxes = self._non_max_suppression(template_boxes, overlap_threshold=0.3)

            # 找到保留的候选
            for kept_box in nms_boxes:
                for i, (x, y, _) in enumerate(template_candidates):
                    if kept_box[0] == x and kept_box[1] == y:
                        kept_indices.append(i)
                        break

            template_candidates = [template_candidates[i] for i in kept_indices]

        # 按相似度排序并限制数量
        template_candidates.sort(key=lambda x: x[2], reverse=True)
        template_candidates = template_candidates[:max_template_results]

        print(f"   找到 {len(template_candidates)} 个template候选（去重后）")

        if not template_candidates:
            return []

        # 第二阶段：MSE精确验证
        print(f"🎯 第二阶段：MSE精确验证...")

        verified_matches = []
        perfect_match_found = False

        for i, (cand_x, cand_y, template_sim) in enumerate(template_candidates):
            # 提取候选区域
            region = large_img[cand_y:cand_y+h, cand_x:cand_x+w]

            # 检查区域大小
            if region.shape[:2] != (h, w):
                continue

            # MSE验证
            mse_similarity = self._compare_mse(region, template)

            print(f"   候选{i+1}: 位置({cand_x}, {cand_y}) template={template_sim:.3f} mse={mse_similarity:.3f}")

            # 通过MSE验证
            if mse_similarity >= mse_threshold:
                verified_matches.append((cand_x, cand_y, mse_similarity))

                # 🎯 完全匹配优化：发现完全匹配时的处理
                if mse_similarity >= 0.999:
                    print(f"   🎉 发现完全匹配！MSE={mse_similarity:.3f}")
                    perfect_match_found = True
                    # 对于多匹配模式，找到完全匹配后继续搜索其他可能的完全匹配
                    # 但可以提高后续候选的验证阈值
                    mse_threshold = max(mse_threshold, 0.999)

            # 如果已经找到足够多的高质量匹配，可以提前停止
            if len(verified_matches) >= max_final_results and perfect_match_found:
                print(f"   ⚡ 已找到{len(verified_matches)}个高质量匹配（包含完全匹配），提前停止")
                break

        # 按MSE相似度排序
        verified_matches.sort(key=lambda x: x[2], reverse=True)

        print(f"   ✅ {len(verified_matches)} 个候选通过MSE验证")

        return verified_matches[:max_final_results]

    def smart_find_image(self,
                         large_img_input: Union[str, np.ndarray],
                         template_input: Union[str, np.ndarray],
                         # ORB parameters (primary attempt)
                         min_good_matches_orb: int = 10,
                         orb_features_count: int = 1000, # Defaulting to a higher count
                         orb_fallback_enabled: bool = True, # Controls if fallback occurs
                         # Hybrid parameters (for fallback or direct call if method='hybrid')
                         hybrid_template_threshold: float = 0.3,
                         hybrid_mse_threshold: float = 0.75,
                         # Control which method to use primarily, or if fallback logic is active
                         # 'orb_then_hybrid': Tries ORB, falls back to hybrid 'best'.
                         # 'orb_only': Only ORB.
                         # 'hybrid_only_best': Only Hybrid 'best' mode.
                         # 'hybrid_only_all': Only Hybrid 'all' mode.
                         preferred_method_strategy: str = 'orb_then_hybrid'
                         ) -> Optional[Union[Tuple[int, int, int, int, Union[int, float]], List[Tuple[int, int, float]]]]:
        """
        智能图像搜索：优先尝试ORB，若失败或效果不佳，可回退到混合方法。

        Args:
            large_img_input: 大图（文件路径或numpy数组）。
            template_input: 模板图像（文件路径或numpy数组）。
            min_good_matches_orb: (ORB) 最小优质匹配点数。
            orb_features_count: (ORB) ORB检测器最大特征点数。
            orb_fallback_enabled: (If ORB is primary) 是否在ORB失败时启用回退到Hybrid。
            hybrid_template_threshold: (Hybrid) 模板匹配阶段的阈值。
            hybrid_mse_threshold: (Hybrid) MSE验证阶段的阈值。
            preferred_method_strategy: 控制搜索策略：
                'orb_then_hybrid': 先ORB，失败后Hybrid 'best'。 (默认)
                'orb_only': 仅使用ORB。
                'hybrid_only_best': 仅使用Hybrid 'best'。
                'hybrid_only_all': 仅使用Hybrid 'all'。

        Returns:
            - Tuple (x, y, width, height, score) for ORB or Hybrid 'best'. Score is num_good_matches for ORB, mse_similarity for Hybrid.
            - List[(x, y, mse_similarity), ...] for Hybrid 'all'.
            - None if no match found.
        """
        large_img_np: Optional[np.ndarray] = None
        if isinstance(large_img_input, str):
            large_img_np = cv2.imread(large_img_input)
            if large_img_np is None: print(f"Error: Could not load large image from path: {large_img_input}"); return None
        elif isinstance(large_img_input, np.ndarray):
            large_img_np = large_img_input.copy()
        else: print(f"Error: large_img_input must be path string or NumPy array."); return None

        template_img_np: Optional[np.ndarray] = None
        if isinstance(template_input, str):
            template_img_np = cv2.imread(template_input)
            if template_img_np is None: print(f"Error: Could not load template image from path: {template_input}"); return None
        elif isinstance(template_input, np.ndarray):
            template_img_np = template_input.copy()
        else: print(f"Error: template_input must be path string or NumPy array."); return None

        # Ensure template_img_np is valid before proceeding (e.g. for hybrid fallback)
        if template_img_np is None: # Should have been caught but as a safeguard
            print(f"Error: Template image is None before method selection.")
            return None


        orb_match_result = None
        attempt_orb = False
        attempt_hybrid_best = False
        attempt_hybrid_all = False

        if preferred_method_strategy == 'orb_then_hybrid':
            attempt_orb = True
            # Fallback to hybrid_best if ORB fails is handled below
        elif preferred_method_strategy == 'orb_only':
            attempt_orb = True
        elif preferred_method_strategy == 'hybrid_only_best':
            attempt_hybrid_best = True
        elif preferred_method_strategy == 'hybrid_only_all':
            attempt_hybrid_all = True
        else:
            print(f"Error: Unknown preferred_method_strategy: {preferred_method_strategy}"); return None

        if attempt_orb:
            print(f"Attempting ORB match: min_good_matches={min_good_matches_orb}, orb_features={orb_features_count}")
            orb_match_result = self._find_image_orb_internal(large_img_np, template_img_np,
                                                             min_good_matches=min_good_matches_orb,
                                                             orb_features=orb_features_count)
            if orb_match_result:
                print(f"ORB method succeeded.")
                return orb_match_result # (x, y, w, h, num_good_matches)
            else:
                print(f"ORB method failed or found no confident match.")
                if preferred_method_strategy == 'orb_then_hybrid' and orb_fallback_enabled:
                    print("Falling back to Hybrid 'best' method.")
                    attempt_hybrid_best = True
                elif preferred_method_strategy == 'orb_only':
                    return None # ORB only was requested and it failed

        if attempt_hybrid_best:
            print(f"Using Hybrid 'best' method: template_thresh={hybrid_template_threshold}, mse_thresh={hybrid_mse_threshold}")
            hybrid_match = self.find_best_match_hybrid(large_img_np, template_img_np,
                                                       template_threshold=hybrid_template_threshold,
                                                       mse_threshold=hybrid_mse_threshold)
            if hybrid_match:
                x, y, mse_similarity = hybrid_match
                th, tw = template_img_np.shape[:2] # template_img_np is guaranteed to be valid here
                return x, y, tw, th, mse_similarity
            print(f"Hybrid 'best' method found no match.")
            return None

        if attempt_hybrid_all:
            print(f"Using Hybrid 'all' method: template_thresh={hybrid_template_threshold}, mse_thresh={hybrid_mse_threshold}")
            # Returns List[(x,y,similarity)] or empty list
            all_hybrid_matches = self.find_all_matches_hybrid(large_img_np, template_img_np,
                                                              template_threshold=hybrid_template_threshold,
                                                              mse_threshold=hybrid_mse_threshold)
            if not all_hybrid_matches: # Empty list means no matches
                 print(f"Hybrid 'all' method found no matches.")
                 return None
            return all_hybrid_matches

        return None # Should be covered by specific strategy returns

    def draw_match_rectangles(self, image: np.ndarray,
                             matches: Union[Tuple[int, int, int, int, Union[int, float]], List[Tuple[int, int, float]], None],
                             template_size_for_hybrid_all: Optional[Tuple[int, int]] = None,
                             color: Tuple[int, int, int] = (0, 255, 0),
                             thickness: int = 2,
                             show_info: bool = True) -> np.ndarray:
        """
        在图像上绘制匹配结果的矩形框。
        能处理 smart_find_image 返回的各种匹配格式。

        Args:
            image: 原始图像（numpy数组）。
            matches: 匹配结果。
                     - ORB or Hybrid 'best': 单个匹配 (x, y, w, h, score)
                     - Hybrid 'all': 匹配列表 [(x, y, similarity), ...]
                     - None: 无匹配。
            template_size_for_hybrid_all: (仅 Hybrid 'all' 需要) 模板图像尺寸 (width, height)。
            color: 矩形框颜色 (B, G, R)，默认绿色。
            thickness: 线条粗细，默认2。
            show_info: 是否显示相似度信息，默认True。

        Returns:
            绘制了矩形框的图像。
        """
        result_img = image.copy()

        if matches is None: # No match found
            return result_img

        # Standardize input `matches` to a list of tuples, each (x, y, w, h, score_text)
        processed_matches = []

        if isinstance(matches, tuple) and len(matches) == 5: # ORB or Hybrid 'best' (x,y,w,h,score)
            x, y, w, h, score = matches
            score_text = f"Matches: {score}" if isinstance(score, int) else f"Sim: {score:.3f}"
            processed_matches.append((x, y, w, h, score_text))
        elif isinstance(matches, list) and all(isinstance(m, tuple) and len(m) == 3 for m in matches): # Hybrid 'all'
            if template_size_for_hybrid_all is None:
                print("❌ Error: template_size_for_hybrid_all must be provided for hybrid 'all' matches.")
                return result_img # Return original image if template_size is missing
            w, h = template_size_for_hybrid_all
            for x, y, score in matches:
                score_text = f"Sim: {score:.3f}"
                processed_matches.append((x, y, w, h, score_text))
        elif not matches: # Handles empty list case for Hybrid 'all'
             pass # No matches to draw
        else:
            print(f"❌ draw_match_rectangles: 无效的匹配结果格式: {type(matches)}")
            return result_img

        for i, (x, y, w, h, info_text) in enumerate(processed_matches):
            top_left = (int(x), int(y))
            bottom_right = (int(x + w), int(y + h))
            cv2.rectangle(result_img, top_left, bottom_right, color, thickness)

            if show_info:
                text_x_coord = int(x)
                text_y_coord = int(y - 10) if y > 30 else int(y + h + 20)
                (text_w_size, text_h_size), _ = cv2.getTextSize(info_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 1)
                bg_tl = (text_x_coord, text_y_coord - text_h_size - 5)
                bg_br = (text_x_coord + text_w_size + 5, text_y_coord + 5)

                cv2.rectangle(result_img, bg_tl, bg_br, (0,0,0), -1) # Black background
                cv2.putText(result_img, info_text, (text_x_coord, text_y_coord),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 1) # White text

                if len(processed_matches) > 1 and i < 9: # Add number for multiple matches (up to 9 for neatness)
                    number_text = f"#{i+1}"
                    cv2.putText(result_img, number_text, (text_x_coord, text_y_coord + 20),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        return result_img

    def save_marked_image(self,
                          image: np.ndarray,
                          save_path: str,
                          matches_data: Optional[Union[Tuple[int, int, int, int, Union[int, float]], List[Tuple[int, int, float]]]],
                          template_input_for_hybrid_all: Optional[Union[str, np.ndarray]] = None,
                          color: Tuple[int, int, int] = (0, 255, 0),
                          show_info: bool = True) -> bool:
        """
        保存标记了匹配位置的图像。

        Args:
            image: 原始图像。
            save_path: 保存路径。
            matches_data: smart_find_image 返回的匹配结果。
            template_input_for_hybrid_all: (仅 Hybrid 'all' 需要) 模板图像（用于获取尺寸）。
            color: 矩形框颜色。
            show_info: 是否显示信息。

        Returns:
            是否保存成功。
        """
        try:
            current_template_size = None
            # Check if matches_data is from hybrid 'all' to determine if template_size is needed
            if isinstance(matches_data, list) and matches_data and isinstance(matches_data[0], tuple) and len(matches_data[0]) == 3:
                if template_input_for_hybrid_all is None:
                    print("❌ Error: template_input_for_hybrid_all is required for hybrid 'all' matches.")
                    return False

                if isinstance(template_input_for_hybrid_all, str):
                    template = cv2.imread(template_input_for_hybrid_all)
                else:
                    template = template_input_for_hybrid_all # Assuming it's already a np.ndarray

                if template is None:
                    print("❌ 无法获取模板图像尺寸 for hybrid 'all' match marking.")
                    return False
                template_h, template_w = template.shape[:2]
                current_template_size = (template_w, template_h)

            marked_image = self.draw_match_rectangles(
                image, matches_data, template_size_for_hybrid_all=current_template_size, color=color, show_info=show_info
            )

            success = cv2.imwrite(save_path, marked_image)
            if success:
                print(f"✅ 标记图像已保存: {save_path}")
            else:
                print(f"❌ 保存失败: {save_path}")
            return success
        except Exception as e:
            print(f"❌ 保存标记图像时出错: {e}")
            return False

    def _find_image_orb_internal(self,
                                 large_img_np: np.ndarray,
                                 template_img_np: np.ndarray,
                                 min_good_matches: int = 10,
                                 orb_features: int = 500) -> Optional[Tuple[int, int, int, int, int]]:
        """
        Internal helper to find template in large image using ORB.

        Args:
            large_img_np: The large image (haystack) as a NumPy array (BGR).
            template_img_np: The template image (needle) as a NumPy array (BGR).
            min_good_matches: Minimum number of good matches required to consider a find.
            orb_features: Max number of features ORB should detect.

        Returns:
            A tuple (x, y, width, height, num_good_matches) if found, else None.
        """
        if template_img_np is None or large_img_np is None:
            print("Error: Template or large image is None in ORB.")
            return None

        if template_img_np.shape[0] == 0 or template_img_np.shape[1] == 0:
            print("Error: Template image has zero dimension in ORB.")
            return None

        # Convert images to grayscale for feature detection
        template_gray = cv2.cvtColor(template_img_np, cv2.COLOR_BGR2GRAY)
        large_gray = cv2.cvtColor(large_img_np, cv2.COLOR_BGR2GRAY)

        # Initialize ORB detector
        # You can adjust nfeatures as needed. More features might find more matches but is slower.
        orb = cv2.ORB_create(nfeatures=orb_features)

        # Find keypoints and descriptors for template
        kp_template, des_template = orb.detectAndCompute(template_gray, None)
        if des_template is None or len(kp_template) == 0:
            print("Warning: No descriptors found for template image in ORB.")
            return None

        # Find keypoints and descriptors for large image
        kp_large, des_large = orb.detectAndCompute(large_gray, None)
        if des_large is None or len(kp_large) == 0:
            print("Warning: No descriptors found for large image in ORB.")
            return None

        # Create BFMatcher object
        # NORM_HAMMING is used for ORB. crossCheck=True gives better matches.
        bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)

        # Match descriptors
        try:
            matches = bf.match(des_template, des_large)
        except cv2.error as e:
            if "query descriptor of type CV_8U" in str(e) and "train descriptor of type CV_8U" in str(e):
                 print(f"Warning: OpenCV BFMatcher error - likely due to empty descriptors even after checks. Error: {e}")
                 return None
            raise e


        # Sort them in the order of their distance (best matches first).
        matches = sorted(matches, key=lambda x: x.distance)

        # Keep only good matches
        # The number of "good" matches can be a parameter.
        # For simplicity, let's take the top 'min_good_matches * 2' to give findHomography a chance
        # and then check if the actual good_matches count is sufficient.
        # This is a heuristic; robust filtering might involve Lowe's ratio test with FLANN,
        # but BFMatcher with crossCheck is already somewhat selective.

        if len(matches) < min_good_matches:
            print(f"Not enough matches found - {len(matches)}/{min_good_matches}")
            return None

        # Consider all sorted matches up to a certain reasonable limit for homography
        # or use a distance threshold if preferred. For now, let's use the top N.
        # For homography, we need at least 4 points.
        num_potential_matches = max(min_good_matches, len(matches)) # Use all if fewer than min_good_matches passed initial check, or all if many

        # Extract location of good matches
        src_pts = np.float32([kp_template[m.queryIdx].pt for m in matches[:num_potential_matches]]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp_large[m.trainIdx].pt for m in matches[:num_potential_matches]]).reshape(-1, 1, 2)

        if len(src_pts) < 4 or len(dst_pts) < 4:
            print(f"Not enough points for homography after filtering: {len(src_pts)} points.")
            return None

        # Find homography
        # RANSAC is a robust method to estimate homography
        M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)

        if M is None:
            print("Could not find Homography matrix.")
            return None

        # Get the actual number of inliers (good matches according to RANSAC)
        # The mask is an array where each element is 1 for inlier, 0 for outlier.
        num_actual_good_matches = np.sum(mask)

        if num_actual_good_matches < min_good_matches:
            print(f"Not enough robust matches after RANSAC - {num_actual_good_matches}/{min_good_matches}")
            return None

        # Get corners of the template image
        h, w = template_img_np.shape[:2]
        pts_template_corners = np.float32([[0, 0], [0, h - 1], [w - 1, h - 1], [w - 1, 0]]).reshape(-1, 1, 2)

        # Transform template corners to large image perspective
        dst_corners = cv2.perspectiveTransform(pts_template_corners, M)

        # Get bounding box (x, y, width, height) from the transformed corners
        x, y, width, height = cv2.boundingRect(dst_corners)

        # For debugging, can draw matches:
        # img_matches = cv2.drawMatches(template_img_np, kp_template, large_img_np, kp_large, matches[:num_actual_good_matches], None, flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS)
        # cv2.imwrite("orb_matches_debug.png", img_matches)
        # large_img_np_copy = large_img_np.copy()
        # cv2.polylines(large_img_np_copy, [np.int32(dst_corners)], True, (0,255,0), 3, cv2.LINE_AA)
        # cv2.imwrite("orb_detected_region.png", large_img_np_copy)


        return (x, y, width, height, int(num_actual_good_matches))


    def find_and_mark_matches(self, large_img_input: Union[str, np.ndarray],
                             template_input: Union[str, np.ndarray],
                             save_path: str,
                             mode: str = 'best',
                             template_threshold: float = 0.7,
                             mse_threshold: float = 0.9,
                             color: Tuple[int, int, int] = (0, 255, 0),
                             show_info: bool = True) -> Union[Optional[Tuple[int, int, float]], List[Tuple[int, int, float]]]:
        """
        一站式服务：搜索匹配并保存标记图像

        Args:
            large_img_input: 大图（文件路径或numpy数组）
            template_input: 模板图像（文件路径或numpy数组）
            save_path: 标记图像保存路径
            mode: 搜索模式 ('best' 或 'all')
            template_threshold: template阈值
            mse_threshold: MSE阈值
            color: 矩形框颜色 (B, G, R)
            show_info: 是否显示相似度信息

        Returns:
            搜索结果
        """
        # 搜索匹配
        matches = self.smart_find_image(
            large_img_input, template_input, mode,
            template_threshold, mse_threshold
        )

        if matches:
            # 获取原始图像
            if isinstance(large_img_input, str):
                image = cv2.imread(large_img_input)
            else:
                image = large_img_input

            # 保存标记图像
            self.save_marked_image(image, save_path, matches, template_input, color, show_info)

            # 打印结果信息
            if isinstance(matches, tuple):
                x, y, similarity = matches
                print(f"🎯 找到匹配: 位置({x}, {y}), 相似度: {similarity:.3f}")
            else:
                print(f"🎯 找到 {len(matches)} 个匹配:")
                for i, (x, y, similarity) in enumerate(matches):
                    print(f"   匹配{i+1}: 位置({x}, {y}), 相似度: {similarity:.3f}")
        else:
            print("❌ 未找到匹配")

        return matches

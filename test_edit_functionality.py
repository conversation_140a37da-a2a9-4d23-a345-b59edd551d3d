#!/usr/bin/env python3
"""
测试脚本编辑功能
"""

import tkinter as tk
from tkinter import ttk
import numpy as np

# 模拟脚本步骤数据
test_script_steps = [
    {
        "template_image": np.zeros((50, 50, 3), dtype=np.uint8),
        "offset_x": 10,
        "offset_y": 20,
        "action_type": "Left Click",
        "key_to_press": "",
        "template_name": "测试模板1",
        "step_interval": 1.0
    },
    {
        "template_image": np.zeros((50, 50, 3), dtype=np.uint8),
        "offset_x": -5,
        "offset_y": 15,
        "action_type": "Press Key",
        "key_to_press": "Enter",
        "template_name": "测试模板2",
        "step_interval": 2.0
    }
]

def test_edit_dialog():
    """测试编辑对话框功能"""
    root = tk.Tk()
    root.title("测试编辑功能")
    root.geometry("400x300")
    
    # 创建一个简化的测试类
    class TestEditDialog:
        def __init__(self, root):
            self.root = root
            self.script_steps = test_script_steps.copy()
            
        def _validate_time_input(self, value_str, field_name="时间"):
            """验证时间输入的有效性"""
            try:
                value = float(value_str.strip())
                if value < 0:
                    tk.messagebox.showerror("输入错误", f"{field_name}不能为负数")
                    return None
                return value
            except ValueError:
                tk.messagebox.showerror("输入错误", f"请输入有效的{field_name}数值")
                return None

        def _validate_offset_input(self, value_str, field_name="偏移"):
            """验证偏移输入"""
            try:
                value = int(value_str.strip())
                if value < -1000 or value > 1000:
                    tk.messagebox.showerror("输入错误", f"{field_name}值应在-1000到1000之间")
                    return None
                return value
            except ValueError:
                tk.messagebox.showerror("输入错误", f"请输入有效的{field_name}整数值")
                return None
                
        def _create_step_edit_dialog(self, title, current_interval, current_offset_x, current_offset_y, info_text, save_callback):
            """创建综合的步骤编辑对话框，支持编辑时间间隔和偏移信息"""
            dialog = tk.Toplevel(self.root)
            dialog.title(title)
            dialog.geometry("450x400")
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            # 信息显示
            info_frame = ttk.Frame(dialog)
            info_frame.pack(fill="x", padx=10, pady=5)
            ttk.Label(info_frame, text=info_text, font=('Segoe UI', 9), justify=tk.LEFT).pack()

            # 分隔线
            ttk.Separator(dialog, orient='horizontal').pack(fill="x", padx=10, pady=5)

            # 时间间隔编辑
            time_frame = ttk.Frame(dialog)
            time_frame.pack(fill="x", padx=10, pady=5)
            
            ttk.Label(time_frame, text="等待时间 (秒):").pack(side=tk.LEFT)
            time_var = tk.StringVar(value=str(current_interval))
            time_entry = ttk.Entry(time_frame, textvariable=time_var, width=10)
            time_entry.pack(side=tk.LEFT, padx=(5, 0))

            # 时间快捷设置按钮
            time_quick_frame = ttk.Frame(dialog)
            time_quick_frame.pack(fill="x", padx=10, pady=2)
            ttk.Label(time_quick_frame, text="快捷设置:").pack(side=tk.LEFT)
            
            time_quick_values = [0.1, 0.5, 1.0, 2.0, 5.0]
            for val in time_quick_values:
                btn = ttk.Button(time_quick_frame, text=f"{val}s", width=6,
                               command=lambda v=val: time_var.set(str(v)))
                btn.pack(side=tk.LEFT, padx=2)

            # 分隔线
            ttk.Separator(dialog, orient='horizontal').pack(fill="x", padx=10, pady=10)

            # 偏移编辑
            offset_frame = ttk.Frame(dialog)
            offset_frame.pack(fill="x", padx=10, pady=5)
            
            # X偏移
            x_frame = ttk.Frame(offset_frame)
            x_frame.pack(fill="x", pady=2)
            ttk.Label(x_frame, text="X 偏移 (像素):").pack(side=tk.LEFT)
            offset_x_var = tk.StringVar(value=str(current_offset_x))
            offset_x_entry = ttk.Entry(x_frame, textvariable=offset_x_var, width=10)
            offset_x_entry.pack(side=tk.LEFT, padx=(5, 0))

            # Y偏移
            y_frame = ttk.Frame(offset_frame)
            y_frame.pack(fill="x", pady=2)
            ttk.Label(y_frame, text="Y 偏移 (像素):").pack(side=tk.LEFT)
            offset_y_var = tk.StringVar(value=str(current_offset_y))
            offset_y_entry = ttk.Entry(y_frame, textvariable=offset_y_var, width=10)
            offset_y_entry.pack(side=tk.LEFT, padx=(5, 0))

            # 偏移快捷设置按钮
            offset_quick_frame = ttk.Frame(dialog)
            offset_quick_frame.pack(fill="x", padx=10, pady=5)
            ttk.Label(offset_quick_frame, text="偏移快捷设置:").pack(anchor="w")
            
            # 创建按钮网格
            button_grid_frame = ttk.Frame(offset_quick_frame)
            button_grid_frame.pack(fill="x", pady=2)
            
            offset_quick_values = [0, -20, -10, -5, 5, 10, 20]
            for i, val in enumerate(offset_quick_values):
                btn_text = "0" if val == 0 else f"{val:+d}"
                
                # X偏移按钮
                x_btn = ttk.Button(button_grid_frame, text=f"X{btn_text}", width=6,
                                 command=lambda v=val: offset_x_var.set(str(v)))
                x_btn.grid(row=0, column=i, padx=1, pady=1)
                
                # Y偏移按钮
                y_btn = ttk.Button(button_grid_frame, text=f"Y{btn_text}", width=6,
                                 command=lambda v=val: offset_y_var.set(str(v)))
                y_btn.grid(row=1, column=i, padx=1, pady=1)

            # 分隔线
            ttk.Separator(dialog, orient='horizontal').pack(fill="x", padx=10, pady=10)

            # 按钮
            button_frame = ttk.Frame(dialog)
            button_frame.pack(fill="x", padx=10, pady=5)

            def save_changes():
                # 验证时间输入
                new_interval = self._validate_time_input(time_var.get(), "等待时间")
                if new_interval is None:
                    return
                
                # 验证偏移输入
                new_offset_x = self._validate_offset_input(offset_x_var.get(), "X偏移")
                if new_offset_x is None:
                    return
                    
                new_offset_y = self._validate_offset_input(offset_y_var.get(), "Y偏移")
                if new_offset_y is None:
                    return
                
                # 调用保存回调
                save_callback(new_interval, new_offset_x, new_offset_y)
                dialog.destroy()

            def cancel_changes():
                dialog.destroy()

            ttk.Button(button_frame, text="保存", command=save_changes).pack(side=tk.RIGHT, padx=(5, 0))
            ttk.Button(button_frame, text="取消", command=cancel_changes).pack(side=tk.RIGHT)

            # 绑定快捷键
            dialog.bind('<Return>', lambda e: save_changes())
            dialog.bind('<Escape>', lambda e: cancel_changes())

            # 设置初始焦点
            time_entry.select_range(0, tk.END)
            time_entry.focus()

            return dialog
            
        def test_edit_step(self, step_index):
            """测试编辑步骤"""
            if step_index < 0 or step_index >= len(self.script_steps):
                tk.messagebox.showerror("编辑错误", "无效的步骤索引")
                return

            step = self.script_steps[step_index]
            current_interval = step.get('step_interval', 1.0)
            current_offset_x = step.get('offset_x', 0)
            current_offset_y = step.get('offset_y', 0)

            # 生成步骤信息
            info_text = f"脚本步骤 {step_index + 1}\n"
            info_text += f"模板: {step['template_name']}\n"
            info_text += f"当前偏移: ({current_offset_x}, {current_offset_y})\n"
            info_text += f"动作: {step['action_type']}"
            if step['action_type'] == "Press Key":
                info_text += f" [{step['key_to_press']}]"

            def save_callback(new_interval, new_offset_x, new_offset_y):
                # 更新数据
                self.script_steps[step_index]['step_interval'] = new_interval
                self.script_steps[step_index]['offset_x'] = new_offset_x
                self.script_steps[step_index]['offset_y'] = new_offset_y
                # 显示结果
                result_text = f"已更新步骤 {step_index + 1}:\n"
                result_text += f"等待时间: {new_interval}s\n"
                result_text += f"偏移: ({new_offset_x}, {new_offset_y})"
                tk.messagebox.showinfo("保存成功", result_text)

            # 使用新的综合编辑对话框
            self._create_step_edit_dialog(
                f"编辑脚本步骤 {step_index + 1}",
                current_interval,
                current_offset_x,
                current_offset_y,
                info_text,
                save_callback
            )
    
    # 创建测试界面
    test_dialog = TestEditDialog(root)
    
    # 创建按钮来测试编辑功能
    ttk.Label(root, text="测试脚本编辑功能", font=('Arial', 14, 'bold')).pack(pady=10)
    
    for i, step in enumerate(test_script_steps):
        frame = ttk.Frame(root)
        frame.pack(fill="x", padx=10, pady=5)
        
        info = f"步骤 {i+1}: {step['template_name']} - 偏移({step['offset_x']},{step['offset_y']}) - 等待{step['step_interval']}s"
        ttk.Label(frame, text=info).pack(side=tk.LEFT)
        
        ttk.Button(frame, text="编辑", 
                  command=lambda idx=i: test_dialog.test_edit_step(idx)).pack(side=tk.RIGHT)
    
    root.mainloop()

if __name__ == "__main__":
    test_edit_dialog()

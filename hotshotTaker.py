import win32gui
import win32ui
import win32con
from PIL import Image
import wx


class hotshotTaker:
    def __init__(self):
        self.dcObj = None
        self.cDC = None
        self.bmp = None

    def take_screenshot(self, hwnd):
        # 获取窗口的设备上下文DC（Device Context）
        wDC = wx.ScreenDC()

        # 创建与窗口DC兼容的DC
        dc = wx.MemoryDC()
        dc.SelectObject(wx.Bitmap(wDC.GetWidth(), wDC.GetHeight()))

        # 从窗口的DC中拷贝图像到位图对象中
        dc.Blit(0, 0, wDC.GetWidth(), wDC.GetHeight(), wDC, 0, 0)

        # 将位图转换为 PIL 图像
        img = wx.ImageFromBitmap(dc.GetBitmap())
        img = img.ConvertToPIL()

        return img

    def __del__(self):
        """确保资源被释放"""
        if self.cDC:
            self.cDC.DeleteDC()
        if self.dcObj:
            self.dcObj.DeleteDC()
        if self.bmp:
            win32gui.DeleteObject(self.bmp.GetHandle())
        print("资源已释放")

    def cleanup(self):
        """显式释放资源"""
        if self.cDC:
            self.cDC.DeleteDC()
        if self.dcObj:
            self.dcObj.DeleteDC()
        if self.bmp:
            win32gui.DeleteObject(self.bmp.GetHandle())
        print("资源已显式释放")

# 使用示例
# if __name__ == "__main__":
#     screenshot_taker = ScreenshotTaker()
#     hwnd = win32gui.GetDesktopWindow()  # 示例：获取桌面窗口的句柄
#     img = screenshot_taker.take_screenshot(hwnd)
#     img.show()  # 显示截图
#
#     screenshot_taker.cleanup()

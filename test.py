import time

import undetected_chromedriver as uc

options = uc.ChromeOptions()
# setting profile
user_data_dir = "c:\\temp\\profile"

driver = uc.Chrome(options=options, user_data_dir=user_data_dir)

url = 'https://weibo.com/login.php'
driver.get(url)
driver.maximize_window()
print(driver.page_source)
# time.sleep(2)
# driver.window_new()
# driver.get("https://www.baidu.com")

time.sleep(20)
driver.save_screenshot(str(time.time()) + 'datadome_undetected_webddriver.png')
driver.close()
driver.quit()

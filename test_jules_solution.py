#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for optimized smart_find_image in ImageColorScript.
This script loads a desktop screenshot and a template image,
attempts to find the template within the screenshot,
and saves a new image with the found location marked.
"""
from time import sleep

import cv2
from image_color_script import ImageColorScript
import os
from LogiUsbimplementation import ImplementationA
import win32com.client
import pythoncom


def run_test(wyUsbHandle):
    """
    Runs the test to find the template in the desktop image
    and saves the marked result.
    """
    script = ImageColorScript()

    large_image_path = "desktop.png"
    template_image_path = "template1.bmp"
    output_image_path = "marked_desktop_jules_solution.png"

    # Check if image files exist
    # if not os.path.exists(large_image_path):
    #     print(f"Error: Large image file not found at {large_image_path}")
    #     return
    if not os.path.exists(template_image_path):
        print(f"Error: Template image file not found at {template_image_path}")
        return

    print(f"Attempting to find '{template_image_path}' in '{large_image_path}'...")
    print(f"Using default thresholds in smart_find_image (template_threshold=0.4, mse_threshold=0.8)")

    # Load large image to pass as numpy array, ensuring it's read correctly
    large_img_np = script.capture_screen()
    # large_img_np = cv2.imread(large_image_path)
    # if large_img_np is None:
    #     print(f"Error: Could not read large image file {large_image_path} with OpenCV.")
    #     return

    # Load template image to pass as numpy array
    template_img_np = cv2.imread(template_image_path)
    if template_img_np is None:
        print(f"Error: Could not read template image file {template_image_path} with OpenCV.")
        return

    # Parameters for smart_find_image.
    # It will try ORB first, then fallback to Hybrid 'best'.
    # ORB params
    orb_min_good_matches = 10
    orb_feature_count = 1000
    # Hybrid fallback params (if ORB fails)
    hybrid_template_thresh = 0.3
    hybrid_mse_thresh = 0.75

    print(f"Calling smart_find_image (ORB then Hybrid fallback strategy)...")
    print(f"  ORB params: min_good_matches={orb_min_good_matches}, orb_features={orb_feature_count}")
    print(f"  Hybrid fallback params: template_thresh={hybrid_template_thresh}, mse_thresh={hybrid_mse_thresh}")

    match_result = script.smart_find_image(
        large_img_input=large_img_np,
        template_input=template_img_np,
        preferred_method_strategy='orb_then_hybrid',  # Default, but explicit
        min_good_matches_orb=orb_min_good_matches,
        orb_features_count=orb_feature_count,
        hybrid_template_threshold=hybrid_template_thresh,
        hybrid_mse_threshold=hybrid_mse_thresh
        # orb_fallback_enabled is True by default for 'orb_then_hybrid'
    )
    print("match_result", match_result)
    x, y, h, w, thresh = match_result

    # # 获取鼠标坐标
    # mouseCurrentX = win32com.client.VARIANT(pythoncom.VT_BYREF | pythoncom.VT_I4, 0)
    # mouseCurrentY = win32com.client.VARIANT(pythoncom.VT_BYREF | pythoncom.VT_I4, 0)
    # print("mouseCurrentX, mouseCurrentY", mouseCurrentX, mouseCurrentY)
    #
    # if wyUsbHandle.GetCursorPos(mouseCurrentX, mouseCurrentY):
    #     print(f"当前鼠标坐标：({mouseCurrentX.value}, {mouseCurrentY.value})")
    # else:
    #     print("获取鼠标坐标失败")
    if  template_image_path == "template1.bmp":
        wyUsbHandle.MoveTo(x + h / 2+60, y + w / 2)
        sleep(1)
        wyUsbHandle.LeftClick()
        sleep(1)
        wyUsbHandle.LeftClick()
    else:
        wyUsbHandle.MoveTo(x + h / 2, y + w / 2)
        sleep(1)
        wyUsbHandle.LeftClick()
        sleep(1)
        wyUsbHandle.LeftClick()

    print("==" * 50)
    if match_result:
        # smart_find_image with 'orb_then_hybrid' or 'hybrid_only_best'
        # returns a tuple: (x, y, w, h, score)
        # Score is num_good_matches for ORB, mse_similarity for Hybrid.

        # Check if it's a list (would be from hybrid_only_all, which we are not using here)
        if isinstance(match_result, list):
            print(f"Error: Received a list of matches, expected a single best match tuple.")
            # Handle list if necessary, or assume this path isn't taken by current strategy
            # For now, this test focuses on the single best match from ORB or Hybrid fallback.
            if not match_result:
                print("No matches found (empty list).")
                return  # exit if empty list
            # If it was a list, for simplicity, take the first one if we must proceed.
            # This part of the logic would need refinement if 'hybrid_only_all' was the strategy.
            print(f"Processing first match from a list of {len(match_result)} (unexpected for this test).")
            # Assuming hybrid 'all' items are (x,y,score) - need template size for w,h
            th_temp, tw_temp = template_img_np.shape[:2]
            x, y, score = match_result[0]
            w, h = tw_temp, th_temp
            match_type = "Hybrid (from list)"

        elif isinstance(match_result, tuple) and len(match_result) == 5:
            x, y, w, h, score = match_result
            # Determine if score is from ORB (likely int) or Hybrid (float) to describe it.
            # This is a heuristic. A more robust way would be for smart_find_image to return method used.
            if isinstance(score, int):
                match_type = "ORB"
                print(f"Match found via ORB: (x={x}, y={y}), W={w}, H={h} with {score} good matches.")
            else:  # float score assumed from Hybrid
                match_type = "Hybrid (fallback)"
                print(f"Match found via {match_type}: (x={x}, y={y}), W={w}, H={h} with Similarity: {score:.4f}.")

        else:
            print(f"Error: Unknown match_result format: {type(match_result)}")
            return

        target_x, target_y = 262, 219  # User's desired target coordinates
        distance_to_target = abs(x - target_x) + abs(y - target_y)
        print(f"Distance from found match top-left ({x},{y}) to target ({target_x},{target_y}): {distance_to_target}")

        if distance_to_target < 50:  # Arbitrary threshold for "close enough"
            print(f"✅ The {match_type} match is close to the target coordinates ({target_x},{target_y}).")
        else:
            print(f"⚠️ The {match_type} match is NOT very close to the target coordinates ({target_x},{target_y}).")

        # Draw rectangle for the found match
        marked_image = script.draw_match_rectangles(
            image=large_img_np.copy(),
            matches=match_result,  # Pass the (x,y,w,h,score) tuple
            template_size_for_hybrid_all=None,  # Not needed when match_result is a tuple
            color=(0, 255, 0) if match_type == "ORB" else (255, 165, 0),  # Green for ORB, Orange for Hybrid
            thickness=2,
            show_info=True
        )

        try:
            cv2.imwrite(output_image_path, marked_image)
            print(f"Successfully saved marked image ({match_type} result) to '{output_image_path}'")
        except Exception as e:
            print(f"Error saving marked image: {e}")

    else:
        print("No match found by smart_find_image with the 'orb_then_hybrid' strategy.")


if __name__ == "__main__":
    logiUsb = ImplementationA()
    wyUsbHandle = logiUsb.wyhz_init()
    i = 0
    while i < 5000:
        i += 1
        run_test(wyUsbHandle)
        sleep(60)
        print("==" * 50, "当前循环次数", i, "次")

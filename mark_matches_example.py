#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标记匹配位置功能示例
展示如何在截图上标记找到的匹配位置
"""

import cv2
from image_color_script import ImageColorScript

def basic_marking_example():
    """基础标记示例"""
    print("=== 基础标记功能示例 ===")
    print("功能: 搜索 → 标记 → 保存")
    print()
    
    script = ImageColorScript()
    
    try:
        # 1. 截图
        print("📸 截取屏幕...")
        screen_img = script.capture_screen()
        print(f"   截图尺寸: {screen_img.shape}")
        
        # 2. 搜索匹配
        print(f"\n🔍 搜索匹配...")
        result = script.smart_find_image(
            screen_img,
            "template.bmp",
            mode='best',
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        print(f"   搜索结果: {result}")
        
        if result:
            x, y, similarity = result
            print(f"   ✅ 找到匹配: 位置({x}, {y}), 相似度: {similarity:.3f}")
            
            # 3. 手动标记和保存
            print(f"\n🎨 标记匹配位置...")
            
            # 获取模板尺寸
            template = cv2.imread("template.bmp")
            if template is not None:
                template_h, template_w = template.shape[:2]
                
                # 绘制矩形框
                marked_img = script.draw_match_rectangles(
                    screen_img, 
                    result, 
                    (template_w, template_h),
                    color=(0, 255, 0),  # 绿色
                    thickness=3,
                    show_info=True
                )
                
                # 保存标记图像
                save_path = "marked_result_basic.png"
                success = cv2.imwrite(save_path, marked_img)
                
                if success:
                    print(f"   ✅ 标记图像已保存: {save_path}")
                else:
                    print(f"   ❌ 保存失败")
            else:
                print(f"   ❌ 无法加载模板图像")
        else:
            print(f"   ❌ 未找到匹配")
    
    except Exception as e:
        print(f"基础标记示例出错: {e}")

def one_step_marking_example():
    """一步到位标记示例"""
    print("\n=== 一步到位标记示例 ===")
    print("功能: 一个方法完成搜索、标记、保存")
    print()
    
    script = ImageColorScript()
    
    try:
        # 截图
        print("📸 截取屏幕...")
        screen_img = script.capture_screen()
        
        # 一步到位：搜索 + 标记 + 保存
        print(f"\n🚀 一步到位处理...")
        result = script.find_and_mark_matches(
            screen_img,                    # 截图数组
            "template.bmp",                # 模板文件
            "marked_result_onestep.png",   # 保存路径
            mode='best',                   # 搜索模式
            template_threshold=0.7,
            mse_threshold=0.9,
            color=(0, 255, 0),            # 绿色矩形框
            show_info=True                # 显示相似度
        )
        
        print(f"   最终结果: {result}")
    
    except Exception as e:
        print(f"一步到位示例出错: {e}")

def multiple_matches_marking():
    """多个匹配标记示例"""
    print("\n=== 多个匹配标记示例 ===")
    print("功能: 找到所有匹配并用不同颜色标记")
    print()
    
    script = ImageColorScript()
    
    try:
        # 截图
        screen_img = script.capture_screen()
        
        # 搜索所有匹配
        print("🔍 搜索所有匹配...")
        all_matches = script.smart_find_image(
            screen_img,
            "template.bmp",
            mode='all',                    # 搜索所有匹配
            template_threshold=0.6,        # 较低阈值，找更多
            mse_threshold=0.85
        )
        
        if all_matches and len(all_matches) > 0:
            print(f"   找到 {len(all_matches)} 个匹配")
            
            # 标记所有匹配
            result = script.find_and_mark_matches(
                screen_img,
                "template.bmp",
                "marked_result_multiple.png",
                mode='all',
                template_threshold=0.6,
                mse_threshold=0.85,
                color=(255, 0, 0),         # 红色矩形框
                show_info=True
            )
            
            print(f"   所有匹配详情:")
            for i, (x, y, sim) in enumerate(all_matches):
                print(f"     匹配{i+1}: ({x}, {y}) 相似度: {sim:.3f}")
        else:
            print("   未找到多个匹配")
    
    except Exception as e:
        print(f"多匹配标记示例出错: {e}")

def custom_styling_example():
    """自定义样式示例"""
    print("\n=== 自定义样式示例 ===")
    print("功能: 使用不同颜色和样式标记")
    print()
    
    script = ImageColorScript()
    
    try:
        screen_img = script.capture_screen()
        
        # 不同样式的标记
        styles = [
            {
                "name": "高精度匹配",
                "save_path": "marked_high_precision.png",
                "mse_threshold": 0.95,
                "color": (0, 255, 0),      # 绿色
                "thickness": 3
            },
            {
                "name": "中等精度匹配", 
                "save_path": "marked_medium_precision.png",
                "mse_threshold": 0.85,
                "color": (0, 165, 255),    # 橙色
                "thickness": 2
            },
            {
                "name": "低精度匹配",
                "save_path": "marked_low_precision.png", 
                "mse_threshold": 0.75,
                "color": (0, 0, 255),      # 红色
                "thickness": 2
            }
        ]
        
        for style in styles:
            print(f"\n🎨 {style['name']}...")
            
            # 搜索匹配
            result = script.smart_find_image(
                screen_img,
                "template.bmp",
                mode='best',
                template_threshold=0.7,
                mse_threshold=style['mse_threshold']
            )
            
            if result:
                # 手动绘制（演示自定义样式）
                template = cv2.imread("template.bmp")
                if template is not None:
                    template_h, template_w = template.shape[:2]
                    
                    marked_img = script.draw_match_rectangles(
                        screen_img,
                        result,
                        (template_w, template_h),
                        color=style['color'],
                        thickness=style['thickness'],
                        show_info=True
                    )
                    
                    cv2.imwrite(style['save_path'], marked_img)
                    print(f"   ✅ 保存: {style['save_path']}")
                    print(f"   匹配: {result}")
            else:
                print(f"   ❌ 未找到匹配（阈值: {style['mse_threshold']}）")
    
    except Exception as e:
        print(f"自定义样式示例出错: {e}")

def practical_usage_demo():
    """实际使用演示"""
    print("\n=== 实际使用演示 ===")
    
    examples = [
        {
            "name": "游戏脚本调试",
            "description": "标记找到的敌人位置，验证识别准确性",
            "code": """
# 游戏脚本调试示例
script = ImageColorScript()

# 截图并搜索敌人
screen = script.capture_screen()
enemy_result = script.find_and_mark_matches(
    screen, 
    "enemy_template.png",
    "debug_enemy_detection.png",
    mode='all',
    template_threshold=0.7,
    mse_threshold=0.9,
    color=(0, 0, 255),  # 红色标记敌人
    show_info=True
)

if enemy_result:
    print(f'发现 {len(enemy_result)} 个敌人')
    # 可以安全地进行攻击
else:
    print('未发现敌人，继续探索')
            """,
        },
        {
            "name": "UI自动化验证",
            "description": "标记找到的按钮位置，确认识别正确",
            "code": """
# UI自动化验证示例
script = ImageColorScript()

screen = script.capture_screen()

# 搜索并标记提交按钮
submit_result = script.find_and_mark_matches(
    screen,
    "submit_button.png", 
    "ui_submit_detection.png",
    mode='best',
    template_threshold=0.8,
    mse_threshold=0.95,
    color=(0, 255, 0),  # 绿色标记
    show_info=True
)

if submit_result:
    x, y, similarity = submit_result
    print(f'找到提交按钮: ({x}, {y}), 精度: {similarity:.3f}')
    if similarity >= 0.9:
        # 高精度才点击
        script.click(x + 25, y + 12)
            """,
        },
        {
            "name": "批量检测标记",
            "description": "一次性检测多个目标并分别标记",
            "code": """
# 批量检测标记示例
script = ImageColorScript()

screen = script.capture_screen()

targets = [
    ("coin.png", "金币", (0, 255, 255)),      # 黄色
    ("gem.png", "宝石", (255, 0, 255)),       # 紫色  
    ("chest.png", "宝箱", (0, 255, 0))        # 绿色
]

all_detections = screen.copy()

for template_file, name, color in targets:
    result = script.smart_find_image(
        screen, template_file,
        mode='all',
        template_threshold=0.7,
        mse_threshold=0.85
    )
    
    if result:
        # 在同一张图上累积标记
        template = cv2.imread(template_file)
        if template is not None:
            h, w = template.shape[:2]
            all_detections = script.draw_match_rectangles(
                all_detections, result, (w, h), 
                color=color, show_info=True
            )
        print(f'找到 {len(result)} 个{name}')

# 保存综合检测结果
cv2.imwrite("batch_detection_result.png", all_detections)
            """,
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['name']}")
        print(f"   场景: {example['description']}")
        print(f"   代码:")
        for line in example['code'].strip().split('\n'):
            print(f"     {line}")

def main():
    """主函数"""
    print("=" * 60)
    print("匹配位置标记功能演示")
    print("=" * 60)
    print("新功能: 在截图上标记找到的匹配位置")
    print("用途: 调试验证、结果可视化、问题诊断")
    print("=" * 60)
    
    basic_marking_example()
    one_step_marking_example()
    multiple_matches_marking()
    custom_styling_example()
    practical_usage_demo()
    
    print("\n" + "=" * 60)
    print("🎉 标记功能演示完成！")
    print("\n✅ 新增功能:")
    print("• draw_match_rectangles() - 绘制矩形框")
    print("• save_marked_image() - 保存标记图像")
    print("• find_and_mark_matches() - 一步到位处理")
    print("\n🎨 支持特性:")
    print("• 自定义矩形框颜色")
    print("• 显示相似度信息")
    print("• 多个匹配标记")
    print("• 自定义线条粗细")
    print("\n🚀 推荐用法:")
    print("result = script.find_and_mark_matches(")
    print("    screen_img, 'template.bmp', 'marked.png')")

if __name__ == "__main__":
    main()

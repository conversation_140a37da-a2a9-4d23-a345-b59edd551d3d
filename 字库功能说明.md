# zhaoTuZhaoSe.py 字库功能扩展

## 概述

为 `zhaoTuZhaoSe.py` 成功添加了完整的字库制作和屏幕找字功能，现在支持：

1. **字库制作功能** - 从图像中提取字符并创建字库
2. **字库管理功能** - 字库的加载、保存、合并和优化
3. **屏幕找字功能** - 基于字库在屏幕上识别和定位文字

## 新增功能

### 1. FontLibraryCreator 类 - 字库制作

**主要功能：**
- 从图像自动提取和分割字符
- 提取字符特征用于识别
- 创建和保存字库文件
- 支持批量处理多个图像

**核心方法：**
```python
creator = FontLibraryCreator()

# 从单个图像创建字库
creator.create_from_image("text_image.png", "示例文字")

# 从多个图像创建字库
image_text_pairs = [
    ("image1.png", "文字1"),
    ("image2.png", "文字2")
]
creator.create_from_multiple_images(image_text_pairs)

# 预览字符分割结果
boxes = creator.preview_segmentation("text_image.png", "preview.png")

# 保存字库
creator.save_library("my_font.json", "我的字库")
```

### 2. FontLibraryManager 类 - 字库管理

**主要功能：**
- 加载和保存字库文件
- 合并多个字库
- 字库优化（去重、压缩）
- 字库分析和统计

**核心方法：**
```python
manager = FontLibraryManager()

# 加载字库
manager.load_library("font1.json", "font1")
manager.load_library("font2.json", "font2")

# 合并字库
manager.merge_libraries(["font1", "font2"], "merged_font")

# 优化字库（去重）
manager.optimize_library("merged_font")

# 分析字库
analysis = manager.analyze_library("merged_font")
print(f"字库包含 {analysis['total_characters']} 个字符")

# 列出字符
characters = manager.list_characters("merged_font")
```

### 3. ImageColorScript 类扩展 - 屏幕找字

**新增方法：**
- `load_font_library()` - 加载字库
- `find_character()` - 查找单个字符
- `find_character_center()` - 查找字符中心坐标
- `find_text()` - 查找文字串
- `find_text_center()` - 查找文字串中心坐标

**使用示例：**
```python
script = ImageColorScript()

# 加载字库
script.load_font_library("my_font.json")

# 查找单个字符
positions = script.find_character("字")
if positions:
    x, y, w, h = positions[0]
    print(f"找到字符在位置：({x}, {y})")

# 查找字符中心并点击
center = script.find_character_center("按钮")
if center:
    script.click(center[0], center[1])

# 查找文字串
text_positions = script.find_text("用户名")
if text_positions:
    x, y, w, h = text_positions[0]
    print(f"找到文字串在位置：({x}, {y})")

# 在指定区域查找
region = (0, 0, 800, 600)
positions = script.find_character("字", region=region, threshold=0.9)
```

## 字库数据格式

字库文件采用 JSON 格式，结构如下：

```json
{
  "version": "1.0",
  "characters": {
    "字符": {
      "templates": [
        {
          "image": "base64编码的图像数据",
          "width": 宽度,
          "height": 高度,
          "features": "特征数据",
          "font_info": {
            "size": "字体大小",
            "style": "字体样式"
          }
        }
      ]
    }
  },
  "metadata": {
    "created_time": "创建时间",
    "total_chars": 字符总数,
    "description": "字库描述"
  }
}
```

## 使用流程

### 1. 制作字库
```python
# 创建字库制作器
creator = FontLibraryCreator()

# 从包含文字的图像创建字库
creator.create_from_image("sample_text.png", "这是示例文字")

# 保存字库
creator.save_library("my_font.json", "我的字库")
```

### 2. 管理字库
```python
# 创建字库管理器
manager = FontLibraryManager()

# 加载字库
manager.load_library("my_font.json", "my_font")

# 优化字库
manager.optimize_library("my_font")
```

### 3. 屏幕找字
```python
# 创建图色脚本实例
script = ImageColorScript()

# 加载字库
script.load_font_library("my_font.json")

# 查找文字
positions = script.find_text("登录")
if positions:
    # 点击找到的文字
    center = script.find_text_center("登录")
    script.click(center[0], center[1])
```

## 文件说明

- `zhaoTuZhaoSe.py` - 主要功能文件，包含所有类和方法
- `font_library_example.py` - 详细的使用示例
- `test_font_features.py` - 功能测试脚本

## 特性优势

1. **自动字符分割** - 智能识别和分割图像中的字符
2. **特征提取** - 提取多种字符特征提高识别准确度
3. **模糊匹配** - 支持相似度阈值，适应不同显示效果
4. **字库优化** - 自动去重和压缩，提高识别效率
5. **区域搜索** - 支持在指定区域查找，提高性能
6. **批量处理** - 支持批量创建和管理字库
7. **完整集成** - 与原有图色功能完美集成

## 注意事项

1. 字库质量直接影响识别准确度，建议使用清晰的文字图像
2. 不同字体、大小、颜色的文字需要分别制作字库
3. 识别性能与字库大小相关，建议定期优化字库
4. 屏幕分辨率变化可能影响识别效果

## 依赖库

确保已安装以下依赖：
```bash
pip install opencv-python numpy mss pyautogui
```

现在 `zhaoTuZhaoSe.py` 已经具备完整的字库制作和屏幕找字功能！

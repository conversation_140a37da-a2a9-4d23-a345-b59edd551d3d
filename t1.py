import wx
import win32gui

def get_screenshot(hwnd):
    # 获取窗口的矩形区域
    rect = win32gui.GetWindowRect(hwnd)

    # 创建与窗口DC兼容的DC
    dc = wx.MemoryDC()
    dc.SelectObject(wx.Bitmap(rect[2] - rect[0], rect[3] - rect[1]))

    # 从窗口的DC中拷贝图像到位图对象中
    dc.Blit(0, 0, rect[2] - rect[0], rect[3] - rect[1], wx.ScreenDC(), rect[0], rect[1])

    # 将位图转换为 PIL 图像
    img = wx.ImageFromBitmap(dc.GetBitmap())
    img = img.ConvertToPIL()

    return img

# 获取窗口句柄
hwnd = win32gui.FindWindow(None, "Fx64 - VMware Workstation")

# 截图
img = get_screenshot(hwnd)

# 保存图像
img.save("screenshot.png")

import sys
import win32com.client
from ctypes import *

class UsbImplementationBase:
    def __init__(self, vid_pid_index: tuple):
        self.vid, self.pid, self.index = vid_pid_index

    def wyhz_init(self):
        # 可选：仅在需要注册时启用以下代码
        try:
            hkmdll = windll.LoadLibrary(r'D:\plugin\x64\wyhkm.dll')
            hkmdll.DllInstall.argtypes = [c_long, c_longlong]
            result = hkmdll.DllInstall(1, 2)  # 参数可能为 (TRUE, NULL)
            if result < 0:
                print("DLL 注册失败")
                sys.exit(1)
        except Exception as e:
            print(f"DLL 注册失败: {e}")

        try:
            # 创建 COM 对象
            wyhkm = win32com.client.Dispatch('wyp.hkm')
        except Exception as e:
            print(f"COM 对象创建失败: {e}")
            sys.exit(1)

        version = wyhkm.GetVersion()
        print(f'盒子版本: {hex(version)}')

        DevId = wyhkm.SearchDevice(self.vid, self.pid, self.index)
        if DevId == -1:
            print("未找到无涯键鼠盒子")
            sys.exit(1)

        if not wyhkm.Open(DevId, 0):
            print("打开键鼠盒子失败")
            sys.exit(1)

        print("注册成功")
        return wyhkm

    @staticmethod
    def wyhkm_close(wyhkm):
        try:
            if hasattr(wyhkm, 'ReleaseKeyboard'):
                wyhkm.ReleaseKeyboard()
            if hasattr(wyhkm, 'ReleaseMouse'):
                wyhkm.ReleaseMouse()
            if hasattr(wyhkm, 'Close'):
                wyhkm.Close()
        except Exception as e:
            print(f"关闭设备时发生错误: {e}")
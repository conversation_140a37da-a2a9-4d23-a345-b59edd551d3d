#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像比较分析脚本
专门分析为什么区域截图和全屏截图的相似度会出现负值
"""

from image_color_script import ImageColorScript

def find_imgPos():
    # 推荐的使用方式
    script = ImageColorScript()
    screen_img= script.capture_screen()

    # # 自动选择最佳策略
    # result = script.smart_find_image(
    #     screen_img,
    #     "template.bmp",
    #     mode='best',  # 'best' 或 'all'
    #     template_threshold=0.5,
    #     mse_threshold=0.9
    # )

    # 一步完成：搜索 + 标记 + 保存
    result = script.find_and_mark_matches(
        screen_img,
        "template.bmp",
        "marked_result.png",
        mode='best',
        template_threshold=0.3,
        mse_threshold=0.8,
        color=(0, 255, 0),
        show_info=True
    )

    print("result:", result)
    print("=" * 50)

    print("result ",result)
    print("=" * 50)


    # 1. 查找小图在大图中的位置（最准确）
    position = script.find_image_in_image("test_full_screen.png", "template.bmp")
    print(position)

    # 2. 多种相似度比较方法
    similarity = script.compare_images("test_full_screen.png", "template.bmp", method='histogram')  # 推荐
    print(similarity)
    
    # 2. 多种相似度比较方法
    similarity = script.compare_images("test_full_screen.png", "template.bmp", method='mse')  # 推荐
    print(similarity)

    # 2. 多种相似度比较方法
    similarity = script.compare_images("test_full_screen.png", "template.bmp", method='ssim')  # 推荐
    print(similarity)

    # 2. 多种相似度比较方法
    similarity = script.compare_images("test_full_screen.png", "template.bmp", method='template')  # 推荐
    print(similarity)


    # 3. 在屏幕中查找图像
    results = script.find_image("template.bmp", threshold=0.6)
    print(results)

def main():
    """主函数"""
    print("=" * 50)
    find_imgPos();

if __name__ == "__main__":
    main()

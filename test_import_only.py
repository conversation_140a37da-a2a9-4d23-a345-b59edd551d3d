#64位的Python使用64位的无涯键鼠盒子模块
#本例子使用的无涯键鼠盒子模块是5.00
import sys
import win32api
import win32com.client
from ctypes import *

#进程内注册插件,模块所在的路径按照实际位置修改
hkmdll = windll.LoadLibrary("D:\\Plugin\\x64\\wyhkm.dll")
hkmdll.DllInstall.argtypes=(c_long,c_longlong)
if hkmdll.DllInstall(1,2)<0:
    print("注册失败!")
    sys.exit(0)

#创建对象
try:
    wyhkm=win32com.client.Dispatch("wyp.hkm")
except:
    print("创建对象失败!")
    sys.exit(0)
#获得模块版本号
version=wyhkm.GetVersion()
print("无涯键鼠盒子模块版本："+hex(version))
#查找设备,这个只是例子,参数中的VID和PID要改成实际值
DevId=wyhkm.SearchDevice(0x048D, 0x7632, 0)
if DevId==-1:
    print("未找到无涯键鼠盒子")
    sys.exit(0)
#打开设备,DPI模式取每个显示器DPI感知
if not wyhkm.Open(DevId,0):
    print("打开无涯键鼠盒子失败")
    sys.exit(0)
#打开资源管理器快捷键Win+E
wyhkm.KeyDown("WIN")
wyhkm.DelayRnd(100,150)
wyhkm.KeyPress("E")
wyhkm.DelayRnd(100,150)
wyhkm.KeyUp("WIN")
x=0
y=0
r=wyhkm.GetCursorPos(x,y)
print(str(x) + "," + str(y))
print(len(r))
print(r)
print(wyhkm.CheckPressedKeys(0))
#关闭设备
wyhkm.Close()

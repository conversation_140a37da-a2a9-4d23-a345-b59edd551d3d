import io
import socket
import threading
import time
import tkinter as tk
import zlib

import win32con
import win32gui
import win32ui
from PIL import Image, ImageTk

from WindowFinder import WindowFinder


def screenshot_window(hwnd, file_name="window_screenshot.png"):
    # 获取窗口的设备上下文（DC）
    wDC = win32gui.GetWindowDC(hwnd)
    dcObj = win32ui.CreateDCFromHandle(wDC)
    cDC = dcObj.CreateCompatibleDC()

    # 获取窗口的大小
    rect = win32gui.GetWindowRect(hwnd)
    width = rect[2] - rect[0]
    height = rect[3] - rect[1]

    # 创建位图对象
    bmp = win32ui.CreateBitmap()
    bmp.CreateCompatibleBitmap(dcObj, width, height)
    cDC.SelectObject(bmp)
    cDC.BitBlt((0, 0), (width, height), dcObj, (0, 0), win32con.SRCCOPY)

    # 将位图转换为PIL可用格式
    bmpinfo = bmp.GetInfo()
    bmpstr = bmp.GetBitmapBits(True)
    img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                           bmpstr, 'raw', 'BGRX', 0, 1)

    # 保存截图
    img.save(file_name)

    # 释放资源
    dcObj.DeleteDC()
    cDC.DeleteDC()
    win32gui.ReleaseDC(hwnd, wDC)
    win32gui.DeleteObject(bmp.GetHandle())

    print(f"截图已保存为 {file_name}")


def enum_child_windows(parent_handle):
    """
    列出所有子窗口句柄
    """
    child_handles = []

    def callback(hwnd, extra):
        child_handles.append(hwnd)
        return True

    win32gui.EnumChildWindows(parent_handle, callback, None)
    return child_handles


def find_child_by_class(parent_handle, class_name):
    """
    查找具有特定类名的子窗口句柄
    """

    def callback(hwnd, class_name):
        if win32gui.GetClassName(hwnd) == class_name:
            print(f"找到子窗口: 句柄={hwnd}, 类名={class_name}")
            return False  # 返回False停止枚举
        return True

    win32gui.EnumChildWindows(parent_handle, callback, class_name)


def take_screenshot(hwnd):
    # 获取窗口的设备上下文DC（Device Context）
    wDC = win32gui.GetWindowDC(hwnd)
    dcObj = win32ui.CreateDCFromHandle(wDC)
    cDC = dcObj.CreateCompatibleDC()

    # 获取窗口尺寸
    rect = win32gui.GetWindowRect(hwnd)
    width = rect[2] - rect[0]
    height = rect[3] - rect[1]

    # 创建位图对象
    bmp = win32ui.CreateBitmap()
    bmp.CreateCompatibleBitmap(dcObj, width, height)
    cDC.SelectObject(bmp)

    # 从窗口的DC中拷贝图像到位图对象中
    cDC.BitBlt((0, 0), (width, height), dcObj, (0, 0), win32con.SRCCOPY)

    # 将位图转换为PIL可处理的格式
    bmpinfo = bmp.GetInfo()
    bmpstr = bmp.GetBitmapBits(True)
    img = Image.frombuffer(
        'RGB',
        (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
        bmpstr, 'raw', 'BGRX', 0, 1)

    # 释放资源
    dcObj.DeleteDC()
    cDC.DeleteDC()
    win32gui.ReleaseDC(hwnd, wDC)
    win32gui.DeleteObject(bmp.GetHandle())

    # 将PIL图像保存到字节流中
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')

    # send_screenshot('192.168.50.222', 12345, img_byte_arr.getvalue())

    return img_byte_arr.getvalue()


def update_image(canvas, hwnd, image_label):
    img = take_screenshot(hwnd)  # 获取最新截图
    imgtk = ImageTk.PhotoImage(image=img)
    image_label.config(image=imgtk)
    image_label.image = imgtk  # 防止被垃圾回收
    canvas.after(10, lambda: update_image(canvas, hwnd, image_label))  # 每10ms更新一次
    # def capture_and_update():
    #     while True:
    #         img_data = take_screenshot(hwnd)  # 使用之前定义的截图函数
    #         time.sleep(0.020)  # 暂停20毫秒
    #
    # # 创建并启动后台线程进行图像更新
    # thread = threading.Thread(target=capture_and_update)
    # thread.daemon = True  # 设为守护线程，确保主程序退出时线程也会退出
    # thread.start()


def on_drag(event):
    # 计算新位置，考虑窗口的宽度和高度
    x = root.winfo_pointerx() - offset_x
    y = root.winfo_pointery() - offset_y

    # 由于标题栏在底部，我们需要根据标题栏的位置调整窗口的y坐标
    # 这里假设标题栏的高度为20，如果不同，需要相应调整
    title_bar_height = 20
    y -= (root.winfo_height() - title_bar_height)

    root.geometry(f'+{x}+{y}')


def start_drag(event):
    global offset_x, offset_y
    # 记录鼠标点击位置相对于窗口的偏移量
    offset_x = event.x
    offset_y = event.y


# 设定想要设置的缓冲区大小（以字节为单位）
sndbuf_size = 1024 * 1024  # 发送缓冲区大小设置为1MB


def send_screenshot(host, port, screenshot_data):
    # 使用zlib压缩图像数据
    compressed_data = zlib.compress(screenshot_data)

    # 发送压缩后的数据
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        # s.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, sndbuf_size)
        s.connect((host, port))
        s.sendall(compressed_data)
        s.close()


if __name__ == '__main__':

    # 使用窗口标题查找父窗口句柄
    parent_handle = win32gui.FindWindow(None, 'Fx64 - VMware Workstation')
    if parent_handle:
        print(f"父窗口句柄: {parent_handle}")

        # 查找具有特定类名的子窗口
        finder = WindowFinder('VMware.CGuestScreenshot')
        child_hwnd = finder.find_child_window(parent_handle)
        if child_hwnd:
            print(f"找到的子窗口句柄: {child_hwnd}")

            root = tk.Tk()
            # 仅设置窗口的启动位置为屏幕左上角外的-1,-1，不设置大小
            offsetSize = '2'
            root.geometry('1366x768+-' + offsetSize + '+-' + offsetSize)
            root.overrideredirect(True)  # 移除窗口边框和标题栏

            # 模拟的标题栏
            title_bar = tk.Frame(root, bg='gray', height=20)
            title_bar.pack(side='bottom', fill='x')  # 放在窗口底部
            title_bar.bind('<Button-1>', start_drag)  # 鼠标左键按下
            title_bar.bind('<B1-Motion>', on_drag)  # 鼠标左键按下并移动

            # 添加红色关闭按钮
            close_button = tk.Button(title_bar, text='X', bg='red', fg='white', command=root.destroy)
            close_button.pack(side='right')

            canvas = tk.Canvas(root, width=1366, height=768)
            canvas.pack()
            img_label = tk.Label(canvas)
            img_label.pack()
            update_image(canvas, child_hwnd, img_label)
            # update_image(None, child_hwnd, None)
            root.mainloop()

        else:
            print("未找到具有指定类名的子窗口")
    else:
        print("未找到指定的父窗口")


#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的相似度搜索功能
"""

def test_new_methods():
    """测试新增的方法"""
    print("=== 测试新增的相似度搜索方法 ===")
    
    try:
        # 测试语法检查
        print("1. 检查新方法语法...")
        with open('image_color_script.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 编译检查语法错误
        compile(content, 'image_color_script.py', 'exec')
        print("   ✓ 语法检查通过")
        
        # 检查新增的方法
        new_methods = [
            'find_similar_regions',
            'find_best_match_region', 
            'find_all_matches_above_threshold',
            '_nms_similarity_results'
        ]
        
        missing_methods = []
        for method in new_methods:
            if f'def {method}(' in content:
                print(f"   ✓ 新方法 {method} 已添加")
            else:
                missing_methods.append(method)
                print(f"   ✗ 新方法 {method} 未找到")
        
        if not missing_methods:
            print("   ✓ 所有新方法都已正确添加")
        else:
            print(f"   ! 缺少方法: {missing_methods}")
        
        # 检查方法参数
        print("\n2. 检查方法参数...")
        
        # 检查 find_similar_regions 方法
        if 'def find_similar_regions(self, large_img_path: str, template_path: str,' in content:
            print("   ✓ find_similar_regions 参数正确")
        else:
            print("   ✗ find_similar_regions 参数有问题")
        
        # 检查返回类型注解
        if 'List[Tuple[int, int, float]]' in content:
            print("   ✓ 返回类型注解正确")
        else:
            print("   ✗ 返回类型注解缺失")
        
        # 检查支持的方法
        supported_methods = ['template', 'histogram', 'mse', 'ssim']
        for method in supported_methods:
            if f"method == '{method}'" in content:
                print(f"   ✓ 支持 {method} 方法")
            else:
                print(f"   ✗ 不支持 {method} 方法")
        
        print("\n3. 检查功能特性...")
        
        # 检查非极大值抑制
        if '_nms_similarity_results' in content:
            print("   ✓ 包含非极大值抑制功能")
        
        # 检查阈值过滤
        if 'threshold' in content and 'similarity >= threshold' in content:
            print("   ✓ 包含阈值过滤功能")
        
        # 检查结果排序
        if 'sort' in content and 'reverse=True' in content:
            print("   ✓ 包含结果排序功能")
        
        # 检查最大结果数限制
        if 'max_results' in content and '[:max_results]' in content:
            print("   ✓ 包含结果数量限制功能")
        
        print("\n✅ 新功能检查完成！")
        
    except SyntaxError as e:
        print(f"   ✗ 语法错误: {e}")
    except Exception as e:
        print(f"   ✗ 检查失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print("\n=== 新功能使用示例 ===")
    
    examples = [
        "# 导入图色脚本",
        "from image_color_script import ImageColorScript",
        "script = ImageColorScript()",
        "",
        "# 1. 查找相似区域（基础用法）",
        "results = script.find_similar_regions(",
        "    'large_image.png',",
        "    'template.png',",
        "    method='template',",
        "    threshold=0.8,",
        "    max_results=5",
        ")",
        "",
        "# 遍历结果",
        "for x, y, similarity in results:",
        "    print(f'位置: ({x}, {y}), 相似度: {similarity:.3f}')",
        "",
        "# 2. 查找最佳匹配",
        "best_match = script.find_best_match_region(",
        "    'screenshot.png',",
        "    'button.png',",
        "    method='histogram'",
        ")",
        "",
        "if best_match:",
        "    x, y, similarity = best_match",
        "    print(f'最佳匹配: ({x}, {y}), 相似度: {similarity:.3f}')",
        "    # 点击找到的位置",
        "    script.click(x + 50, y + 25)  # 假设按钮中心偏移",
        "",
        "# 3. 查找所有高质量匹配",
        "high_quality_matches = script.find_all_matches_above_threshold(",
        "    'game_screen.png',",
        "    'coin.png',",
        "    method='template',",
        "    threshold=0.9,",
        "    max_results=20",
        ")",
        "",
        "print(f'找到 {len(high_quality_matches)} 个金币')",
        "for i, (x, y, similarity) in enumerate(high_quality_matches):",
        "    print(f'金币 {i+1}: 位置({x}, {y}), 匹配度{similarity:.2f}')",
        "",
        "# 4. 不同方法的使用场景",
        "# template - 精确匹配，速度最快",
        "exact_matches = script.find_similar_regions('img.png', 'template.png', 'template', 0.9)",
        "",
        "# histogram - 颜色相似匹配",
        "color_matches = script.find_similar_regions('img.png', 'template.png', 'histogram', 0.7)",
        "",
        "# mse - 像素级精确比较",
        "pixel_matches = script.find_similar_regions('img.png', 'template.png', 'mse', 0.95)",
        "",
        "# ssim - 结构相似性比较",
        "structure_matches = script.find_similar_regions('img.png', 'template.png', 'ssim', 0.8)"
    ]
    
    for line in examples:
        print(f"  {line}")

def show_method_comparison():
    """显示方法比较"""
    print("\n=== 方法比较和选择建议 ===")
    
    comparison = [
        "方法对比:",
        "",
        "1. template (模板匹配)",
        "   • 速度: ⭐⭐⭐⭐⭐ (最快)",
        "   • 精度: ⭐⭐⭐⭐",
        "   • 适用: 精确匹配，形状相同",
        "   • 推荐阈值: 0.8-0.95",
        "",
        "2. histogram (直方图)",
        "   • 速度: ⭐⭐⭐",
        "   • 精度: ⭐⭐⭐",
        "   • 适用: 颜色分布相似",
        "   • 推荐阈值: 0.6-0.8",
        "",
        "3. mse (均方误差)",
        "   • 速度: ⭐⭐",
        "   • 精度: ⭐⭐⭐⭐⭐ (最精确)",
        "   • 适用: 像素级精确比较",
        "   • 推荐阈值: 0.9-0.99",
        "",
        "4. ssim (结构相似性)",
        "   • 速度: ⭐⭐",
        "   • 精度: ⭐⭐⭐⭐",
        "   • 适用: 结构相似性比较",
        "   • 推荐阈值: 0.7-0.9",
        "",
        "选择建议:",
        "• 游戏脚本: 优先使用 template",
        "• 图标识别: 使用 histogram 或 template",
        "• 文字识别: 使用 ssim 或 template",
        "• 精确匹配: 使用 mse 或 template",
        "• 快速搜索: 使用 template"
    ]
    
    for line in comparison:
        print(f"  {line}")

def main():
    """主函数"""
    print("新增相似度搜索功能测试")
    print("=" * 50)
    
    test_new_methods()
    show_usage_examples()
    show_method_comparison()
    
    print("\n" + "=" * 50)
    print("🎉 功能测试完成！")
    print("\n📋 新增功能总结:")
    print("✅ find_similar_regions() - 查找所有相似区域")
    print("✅ find_best_match_region() - 查找最佳匹配")
    print("✅ find_all_matches_above_threshold() - 查找高质量匹配")
    print("✅ 支持4种相似度算法")
    print("✅ 可设置相似度阈值")
    print("✅ 自动去重和排序")
    print("✅ 返回坐标和相似度值")
    print("\n💡 现在您可以:")
    print("• 设置不同的相似度阈值")
    print("• 获取满足条件的所有坐标")
    print("• 选择最适合的比较方法")
    print("• 批量处理多个匹配结果")

if __name__ == "__main__":
    main()

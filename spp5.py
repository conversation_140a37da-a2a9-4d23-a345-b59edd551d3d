import asyncio
import ssl

import win32con
import win32gui
import win32ui
from aioquic.asyncio import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import StreamDataReceived, HandshakeCompleted
import lz4.frame
from PIL import Image
import io

from WindowFinder import WindowFinder  # 确保这个模块在路径中



# 截取特定窗口的截图
def take_screenshot(hwnd):
    wDC = win32gui.GetWindowDC(hwnd)
    dcObj = win32ui.CreateDCFromHandle(wDC)
    cDC = dcObj.CreateCompatibleDC()
    rect = win32gui.GetWindowRect(hwnd)
    width = rect[2] - rect[0]
    height = rect[3] - rect[1]
    bmp = win32ui.CreateBitmap()
    bmp.CreateCompatibleBitmap(dcObj, width, height)
    cDC.SelectObject(bmp)
    cDC.BitBlt((0, 0), (width, height), dcObj, (0, 0), win32con.SRCCOPY)
    bmpinfo = bmp.GetInfo()
    bmpstr = bmp.GetBitmapBits(True)
    img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), bmpstr, 'raw', 'BGRX', 0, 1)

    # 释放资源
    dcObj.DeleteDC()
    cDC.DeleteDC()
    win32gui.ReleaseDC(hwnd, wDC)
    win32gui.DeleteObject(bmp.GetHandle())

    return img


class ScreenShotQuicClientProtocol(QuicConnectionProtocol):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ready_event = asyncio.Event()

    def quic_event_received(self, event):
        if isinstance(event, HandshakeCompleted):
            self.ready_event.set()


async def process_images_and_send_async(protocol, hwnd):
    loop = asyncio.get_running_loop()
    image = await loop.run_in_executor(None, take_screenshot, hwnd)
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='JPEG', quality=85)
    compressed_data = lz4.frame.compress(img_byte_arr.getvalue())

    # 等待连接准备就绪
    await protocol.ready_event.wait()

    # 发送数据
    protocol._quic.send_stream_data(0, compressed_data)
    protocol.transmit()


async def run_client(hwnd, host, port):
    configuration = QuicConfiguration(is_client=True)
    # WARNING: This is insecure, do not use in production!
    configuration.verify_mode = ssl.CERT_NONE


    async with connect(host, port, configuration=configuration,
                       create_protocol=ScreenShotQuicClientProtocol) as protocol:
        await protocol.ready_event.wait()
        while True:
            await process_images_and_send_async(protocol, hwnd)
            await asyncio.sleep(0.020)  # 控制截图发送频率


if __name__ == '__main__':
    # 使用窗口标题查找父窗口句柄
    parent_handle = win32gui.FindWindow(None, 'Fx64 - VMware Workstation')
    if parent_handle:
        print(f"父窗口句柄: {parent_handle}")

        # 查找具有特定类名的子窗口
        finder = WindowFinder('VMware.CGuestScreenshot')
        child_hwnd = finder.find_child_window(parent_handle)
        if child_hwnd:
            print(f"找到的子窗口句柄: {child_hwnd}")
            # Your existing window handle fetching logic
            hwnd, host, port = child_hwnd, '**************', 4433  # Adjust as necessary
            asyncio.run(run_client(hwnd, host, port))

        else:
            print("未找到具有指定类名的子窗口")
    else:
        print("未找到指定的父窗口")

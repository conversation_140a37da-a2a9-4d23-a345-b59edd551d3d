#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试搜索问题
分析为什么没有找到正确的匹配位置
"""

import cv2
import numpy as np
from image_color_script import ImageColorScript

def debug_search_parameters():
    """调试搜索参数"""
    print("=== 调试搜索参数 ===")
    print("问题: 找到位置(40, 195)，正确位置应该是(242, 212)")
    print("分析: 可能是阈值设置或搜索策略问题")
    print()
    
    script = ImageColorScript()
    
    try:
        # 截图
        screen_img = script.capture_screen()
        template_path = "template.bmp"
        
        print("📊 测试不同的阈值组合...")
        
        # 测试不同的阈值组合
        test_cases = [
            {
                "name": "当前设置",
                "template_threshold": 0.7,
                "mse_threshold": 0.9,
                "description": "当前使用的阈值"
            },
            {
                "name": "更宽松Template",
                "template_threshold": 0.5,
                "mse_threshold": 0.9,
                "description": "降低template阈值，找更多候选"
            },
            {
                "name": "更宽松MSE",
                "template_threshold": 0.7,
                "mse_threshold": 0.8,
                "description": "降低MSE阈值"
            },
            {
                "name": "都更宽松",
                "template_threshold": 0.5,
                "mse_threshold": 0.8,
                "description": "两个阈值都降低"
            },
            {
                "name": "纯Template",
                "template_threshold": 0.6,
                "mse_threshold": 0.0,
                "description": "只用template方法"
            }
        ]
        
        for i, case in enumerate(test_cases):
            print(f"\n{i+1}. 测试 {case['name']} ({case['description']})")
            print(f"   Template阈值: {case['template_threshold']}")
            print(f"   MSE阈值: {case['mse_threshold']}")
            
            # 搜索所有匹配
            all_matches = script.find_all_matches_hybrid(
                screen_img,
                template_path,
                template_threshold=case['template_threshold'],
                mse_threshold=case['mse_threshold'],
                max_template_results=50,
                max_final_results=10
            )
            
            if all_matches:
                print(f"   找到 {len(all_matches)} 个匹配:")
                for j, (x, y, similarity) in enumerate(all_matches[:5]):  # 只显示前5个
                    distance_to_correct = abs(x - 242) + abs(y - 212)
                    status = "✅ 接近正确位置" if distance_to_correct < 20 else "❌ 偏离较远"
                    print(f"     匹配{j+1}: ({x}, {y}) MSE={similarity:.3f} 距离正确位置:{distance_to_correct}px {status}")
            else:
                print(f"   ❌ 未找到匹配")
    
    except Exception as e:
        print(f"调试过程出错: {e}")

def test_pure_template_method():
    """测试纯template方法"""
    print("\n=== 测试纯Template方法 ===")
    print("目的: 看看template方法能找到多少候选")
    print()
    
    script = ImageColorScript()
    
    try:
        screen_img = script.capture_screen()
        template_path = "template.bmp"
        
        # 加载模板
        template = cv2.imread(template_path)
        if template is None:
            print("❌ 无法加载模板图像")
            return
        
        # 转换为灰度图
        gray_screen = cv2.cvtColor(screen_img, cv2.COLOR_BGR2GRAY)
        gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        
        # 模板匹配
        result = cv2.matchTemplate(gray_screen, gray_template, cv2.TM_CCOEFF_NORMED)
        
        # 找到所有可能的匹配
        threshold_values = [0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
        
        for threshold in threshold_values:
            locations = np.where(result >= threshold)
            matches = list(zip(*locations[::-1]))
            
            print(f"阈值 {threshold}: 找到 {len(matches)} 个template候选")
            
            if len(matches) > 0:
                # 检查是否包含正确位置附近的匹配
                correct_nearby = []
                for x, y in matches[:20]:  # 检查前20个
                    distance = abs(x - 242) + abs(y - 212)
                    if distance < 30:  # 30像素内认为是接近的
                        similarity = result[y, x]
                        correct_nearby.append((x, y, similarity, distance))
                
                if correct_nearby:
                    print(f"   ✅ 找到接近正确位置的候选:")
                    for x, y, sim, dist in correct_nearby:
                        print(f"      位置({x}, {y}) 相似度:{sim:.3f} 距离:{dist}px")
                else:
                    print(f"   ❌ 没有接近正确位置的候选")
                    # 显示前几个最高分的
                    scored_matches = [(x, y, result[y, x]) for x, y in matches[:10]]
                    scored_matches.sort(key=lambda x: x[2], reverse=True)
                    print(f"   前3个最高分候选:")
                    for x, y, sim in scored_matches[:3]:
                        distance = abs(x - 242) + abs(y - 212)
                        print(f"      位置({x}, {y}) 相似度:{sim:.3f} 距离正确位置:{distance}px")
    
    except Exception as e:
        print(f"纯template测试出错: {e}")

def analyze_template_quality():
    """分析模板质量"""
    print("\n=== 分析模板质量 ===")
    print("检查模板图像是否适合搜索")
    print()
    
    try:
        template_path = "template.bmp"
        template = cv2.imread(template_path)
        
        if template is None:
            print("❌ 无法加载模板图像")
            return
        
        h, w = template.shape[:2]
        print(f"📏 模板尺寸: {w} x {h}")
        
        # 检查模板是否太小
        if w < 20 or h < 20:
            print("⚠️  模板可能太小，建议使用更大的模板")
        elif w > 200 or h > 200:
            print("⚠️  模板可能太大，可能影响匹配精度")
        else:
            print("✅ 模板尺寸合适")
        
        # 检查模板的特征
        gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        
        # 计算梯度强度（边缘特征）
        grad_x = cv2.Sobel(gray_template, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_template, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        avg_gradient = np.mean(gradient_magnitude)
        
        print(f"🔍 平均梯度强度: {avg_gradient:.2f}")
        if avg_gradient < 10:
            print("⚠️  模板特征较弱，可能难以准确匹配")
        elif avg_gradient > 50:
            print("✅ 模板特征丰富，适合匹配")
        else:
            print("✅ 模板特征适中")
        
        # 检查颜色分布
        mean_color = np.mean(template, axis=(0, 1))
        print(f"🎨 平均颜色 (BGR): ({mean_color[0]:.1f}, {mean_color[1]:.1f}, {mean_color[2]:.1f})")
        
        # 保存模板分析图像
        analysis_img = template.copy()
        cv2.putText(analysis_img, f"Size: {w}x{h}", (5, 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        cv2.putText(analysis_img, f"Gradient: {avg_gradient:.1f}", (5, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        cv2.imwrite("template_analysis.png", analysis_img)
        print("💾 模板分析图像已保存: template_analysis.png")
        
    except Exception as e:
        print(f"模板分析出错: {e}")

def suggest_solutions():
    """建议解决方案"""
    print("\n=== 建议解决方案 ===")
    
    solutions = [
        "🔧 可能的解决方案:",
        "",
        "1. 调整搜索阈值:",
        "   • 降低template_threshold到0.5或0.6",
        "   • 降低mse_threshold到0.8或0.85",
        "   • 增加max_template_results到50或100",
        "",
        "2. 改进模板图像:",
        "   • 确保模板图像清晰",
        "   • 使用更大的模板区域",
        "   • 避免包含过多背景",
        "",
        "3. 使用不同的搜索策略:",
        "   • 尝试纯template方法",
        "   • 使用多尺度搜索",
        "   • 调整搜索步长",
        "",
        "4. 调试建议:",
        "   • 保存所有候选位置的标记图像",
        "   • 检查template方法的原始结果",
        "   • 验证正确位置的相似度分数",
        "",
        "5. 代码示例:",
        "   # 更宽松的搜索",
        "   result = script.smart_find_image(",
        "       screen_img, 'template.bmp',",
        "       template_threshold=0.5,  # 降低",
        "       mse_threshold=0.8        # 降低",
        "   )"
    ]
    
    for solution in solutions:
        print(f"  {solution}")

def create_debug_search():
    """创建调试搜索函数"""
    print("\n=== 创建调试搜索函数 ===")
    
    debug_code = '''
def debug_search_detailed(script, screen_img, template_path, correct_x=242, correct_y=212):
    """详细调试搜索过程"""
    print("🔍 详细搜索调试...")
    
    # 1. 测试template方法
    template = cv2.imread(template_path)
    gray_screen = cv2.cvtColor(screen_img, cv2.COLOR_BGR2GRAY)
    gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    
    result = cv2.matchTemplate(gray_screen, gray_template, cv2.TM_CCOEFF_NORMED)
    
    # 检查正确位置的分数
    correct_score = result[correct_y, correct_x]
    print(f"正确位置({correct_x}, {correct_y})的template分数: {correct_score:.3f}")
    
    # 找到最高分位置
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    print(f"Template最高分: {max_val:.3f} 位置: {max_loc}")
    
    # 2. 测试混合搜索
    all_matches = script.find_all_matches_hybrid(
        screen_img, template_path,
        template_threshold=0.5,
        mse_threshold=0.8,
        max_template_results=50,
        max_final_results=20
    )
    
    if all_matches:
        print(f"混合搜索找到 {len(all_matches)} 个匹配")
        for i, (x, y, sim) in enumerate(all_matches):
            distance = abs(x - correct_x) + abs(y - correct_y)
            print(f"  匹配{i+1}: ({x}, {y}) MSE={sim:.3f} 距离={distance}px")
    
    return all_matches

# 使用示例
script = ImageColorScript()
screen_img = script.capture_screen()
debug_search_detailed(script, screen_img, "template.bmp")
    '''
    
    print("💡 调试代码:")
    for line in debug_code.strip().split('\n'):
        print(f"  {line}")

def main():
    """主函数"""
    print("=" * 60)
    print("搜索问题调试分析")
    print("=" * 60)
    print("问题: 找到位置(40, 195)，正确位置(242, 212)")
    print("目标: 分析原因并提供解决方案")
    print("=" * 60)
    
    debug_search_parameters()
    test_pure_template_method()
    analyze_template_quality()
    suggest_solutions()
    create_debug_search()
    
    print("\n" + "=" * 60)
    print("🎯 调试建议总结:")
    print("1. 降低搜索阈值 (template_threshold=0.5, mse_threshold=0.8)")
    print("2. 增加候选数量 (max_template_results=50)")
    print("3. 检查模板图像质量")
    print("4. 使用调试函数详细分析")
    print("5. 考虑使用纯template方法作为对比")

if __name__ == "__main__":
    main()

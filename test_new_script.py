from image_color_script import ImageColorScript

def find_imgPos():
    # 推荐的使用方式
    script = ImageColorScript()

    print("=== 测试图色脚本功能 ===")
    
    try:
        # 1. 查找小图在大图中的位置（最准确）
        print("1. 测试在大图中查找小图...")
        position = script.find_image_in_image("test_full_screen.png", "test_region_screen.png")
        if position:
            print(f"   ✓ 找到位置: {position}")
        else:
            print("   ! 未找到匹配位置")

        # 2. 多种相似度比较方法
        print("\n2. 测试图像相似度比较...")
        similarity = script.compare_images("test_full_screen.png", "test_region_screen.png", method='histogram')
        print(f"   直方图相似度: {similarity:.3f}")
        
        similarity_mse = script.compare_images("test_full_screen.png", "test_region_screen.png", method='mse')
        print(f"   MSE相似度: {similarity_mse:.3f}")

        # 3. 在屏幕中查找图像
        print("\n3. 测试在屏幕中查找图像...")
        results = script.find_image("test_region_screen.png", threshold=0.8)
        if results:
            print(f"   ✓ 找到图像: {results}")
        else:
            print("   ! 未在屏幕中找到图像")
            
        # 4. 测试截图功能
        print("\n4. 测试截图功能...")
        if script.save_screenshot("new_test_screenshot.png"):
            print("   ✓ 截图保存成功")
        else:
            print("   ! 截图保存失败")
            
        # 5. 测试颜色获取
        print("\n5. 测试颜色获取...")
        center_x, center_y = script.screen_width // 2, script.screen_height // 2
        color = script.get_pixel_color(center_x, center_y)
        print(f"   屏幕中心点颜色: {color}")
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=" * 50)
    print("测试新的图色脚本")
    print("=" * 50)
    find_imgPos()

if __name__ == "__main__":
    main()

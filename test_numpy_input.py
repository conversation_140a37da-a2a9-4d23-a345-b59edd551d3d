#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试numpy数组输入功能
验证直接传入截图数组的功能
"""

import cv2
import numpy as np
from image_color_script import ImageColorScript

def test_numpy_array_input():
    """测试numpy数组输入"""
    print("=== 测试numpy数组输入功能 ===")
    print("功能: 支持直接传入截图数组，无需保存文件")
    print()
    
    script = ImageColorScript()
    
    try:
        # 1. 截取屏幕（返回numpy数组）
        print("1. 📸 截取屏幕...")
        screen_img = script.capture_screen()
        print(f"   屏幕截图尺寸: {screen_img.shape}")
        print(f"   数据类型: {type(screen_img)}")
        
        # 2. 加载模板图像
        template_path = "template.bmp"
        print(f"\n2. 📁 加载模板图像: {template_path}")
        
        try:
            template_img = cv2.imread(template_path)
            if template_img is not None:
                print(f"   模板图像尺寸: {template_img.shape}")
                print(f"   数据类型: {type(template_img)}")
            else:
                print(f"   ❌ 无法加载模板图像: {template_path}")
                return
        except Exception as e:
            print(f"   ❌ 加载模板图像失败: {e}")
            return
        
        # 3. 使用numpy数组进行搜索
        print(f"\n3. 🔍 使用numpy数组进行混合搜索...")
        
        # 方式1: 传入numpy数组 + 文件路径
        print("   方式1: 截图数组 + 模板文件路径")
        result1 = script.smart_find_image(
            screen_img,           # numpy数组
            template_path,        # 文件路径
            mode='best',
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        print(f"   结果1: {result1}")
        
        # 方式2: 传入两个numpy数组
        print("\n   方式2: 截图数组 + 模板数组")
        result2 = script.smart_find_image(
            screen_img,           # numpy数组
            template_img,         # numpy数组
            mode='best',
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        print(f"   结果2: {result2}")
        
        # 4. 对比传统文件路径方式
        print(f"\n4. 📊 对比传统文件路径方式...")
        
        # 先保存截图到文件
        screen_file = "temp_screen.png"
        cv2.imwrite(screen_file, screen_img)
        
        # 使用文件路径方式
        result3 = script.smart_find_image(
            screen_file,          # 文件路径
            template_path,        # 文件路径
            mode='best',
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        print(f"   文件路径方式结果: {result3}")
        
        # 5. 结果对比
        print(f"\n5. 🎯 结果对比:")
        print(f"   numpy数组方式1: {result1}")
        print(f"   numpy数组方式2: {result2}")
        print(f"   文件路径方式:   {result3}")
        
        # 检查结果一致性
        results = [result1, result2, result3]
        non_none_results = [r for r in results if r is not None]
        
        if len(non_none_results) > 1:
            # 比较位置是否相近（允许小误差）
            positions = [(r[0], r[1]) for r in non_none_results]
            similarities = [r[2] for r in non_none_results]
            
            print(f"   位置对比: {positions}")
            print(f"   相似度对比: {[f'{s:.3f}' for s in similarities]}")
            
            # 检查位置差异
            if len(set(positions)) == 1:
                print(f"   ✅ 所有方式结果完全一致")
            else:
                max_diff = max(abs(p1[0] - p2[0]) + abs(p1[1] - p2[1]) 
                              for p1 in positions for p2 in positions)
                if max_diff <= 2:
                    print(f"   ✅ 所有方式结果基本一致（最大差异: {max_diff}像素）")
                else:
                    print(f"   ⚠️  结果存在差异（最大差异: {max_diff}像素）")
        
        # 清理临时文件
        import os
        if os.path.exists(screen_file):
            os.remove(screen_file)
    
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 性能对比测试 ===")
    print("对比: numpy数组输入 vs 文件路径输入")
    print()
    
    script = ImageColorScript()
    
    try:
        import time
        
        # 截取屏幕
        screen_img = script.capture_screen()
        template_path = "template.bmp"
        
        # 测试numpy数组方式
        print("🚀 测试numpy数组方式...")
        start_time = time.time()
        
        result_numpy = script.smart_find_image(
            screen_img,
            template_path,
            mode='best',
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        numpy_time = time.time() - start_time
        print(f"   numpy方式耗时: {numpy_time:.3f}秒")
        print(f"   结果: {result_numpy}")
        
        # 测试文件路径方式
        print(f"\n📁 测试文件路径方式...")
        
        # 保存截图到文件
        screen_file = "temp_screen_perf.png"
        save_start = time.time()
        cv2.imwrite(screen_file, screen_img)
        save_time = time.time() - save_start
        
        # 搜索
        search_start = time.time()
        result_file = script.smart_find_image(
            screen_file,
            template_path,
            mode='best',
            template_threshold=0.7,
            mse_threshold=0.9
        )
        search_time = time.time() - search_start
        
        total_file_time = save_time + search_time
        
        print(f"   文件保存耗时: {save_time:.3f}秒")
        print(f"   搜索耗时: {search_time:.3f}秒")
        print(f"   总耗时: {total_file_time:.3f}秒")
        print(f"   结果: {result_file}")
        
        # 性能对比
        print(f"\n📊 性能对比:")
        print(f"   numpy数组方式: {numpy_time:.3f}秒")
        print(f"   文件路径方式: {total_file_time:.3f}秒")
        
        if total_file_time > 0:
            speedup = total_file_time / numpy_time
            print(f"   性能提升: {speedup:.1f}x")
            print(f"   时间节省: {((total_file_time - numpy_time) / total_file_time * 100):.1f}%")
        
        # 清理
        import os
        if os.path.exists(screen_file):
            os.remove(screen_file)
    
    except Exception as e:
        print(f"性能测试出现错误: {e}")

def demonstrate_usage_examples():
    """演示使用示例"""
    print("\n=== 使用示例演示 ===")
    
    examples = [
        {
            "name": "实时游戏脚本",
            "description": "实时截图并搜索，无需保存文件",
            "code": """
# 实时游戏脚本示例
script = ImageColorScript()

while True:
    # 实时截图
    screen = script.capture_screen()
    
    # 直接搜索敌人
    enemy = script.smart_find_image(
        screen,                    # 直接传入截图数组
        "enemy_template.png",      # 模板文件
        mode='best',
        template_threshold=0.7,
        mse_threshold=0.9
    )
    
    if enemy:
        x, y, similarity = enemy
        print(f'发现敌人: ({x}, {y}), 精度: {similarity:.3f}')
        script.click(x + 25, y + 25)  # 攻击
        break
    
    time.sleep(0.1)  # 避免过度占用CPU
            """,
        },
        {
            "name": "UI自动化",
            "description": "截图后批量搜索多个控件",
            "code": """
# UI自动化示例
script = ImageColorScript()

# 一次截图
screen = script.capture_screen()

# 搜索多个控件
controls = [
    ("submit_btn.png", "提交按钮"),
    ("cancel_btn.png", "取消按钮"),
    ("input_field.png", "输入框")
]

for template_file, name in controls:
    result = script.smart_find_image(
        screen,                    # 复用同一张截图
        template_file,
        mode='best',
        template_threshold=0.7,
        mse_threshold=0.9
    )
    
    if result:
        x, y, similarity = result
        print(f'找到{name}: ({x}, {y}), 精度: {similarity:.3f}')
    else:
        print(f'未找到{name}')
            """,
        },
        {
            "name": "高性能搜索",
            "description": "避免重复文件I/O操作",
            "code": """
# 高性能搜索示例
script = ImageColorScript()

# 预加载模板图像
templates = {}
template_files = ["btn1.png", "btn2.png", "btn3.png"]

for file in template_files:
    templates[file] = cv2.imread(file)

# 实时搜索
while True:
    screen = script.capture_screen()
    
    for template_name, template_img in templates.items():
        result = script.smart_find_image(
            screen,                # numpy数组
            template_img,          # numpy数组
            mode='best',
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        if result:
            print(f'找到 {template_name}: {result}')
            break
            """,
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['name']}")
        print(f"   场景: {example['description']}")
        print(f"   代码:")
        for line in example['code'].strip().split('\n'):
            print(f"     {line}")

def main():
    """主函数"""
    print("=" * 60)
    print("numpy数组输入功能测试")
    print("=" * 60)
    print("新功能: 支持直接传入截图数组")
    print("优势: 无需保存临时文件，提升性能")
    print("兼容: 同时支持文件路径和numpy数组")
    print("=" * 60)
    
    test_numpy_array_input()
    test_performance_comparison()
    demonstrate_usage_examples()
    
    print("\n" + "=" * 60)
    print("🎉 numpy数组输入功能测试完成！")
    print("\n✅ 主要优势:")
    print("• 无需保存临时文件")
    print("• 减少磁盘I/O操作")
    print("• 提升整体性能")
    print("• 代码更简洁")
    print("• 内存使用更高效")
    print("\n🚀 推荐用法:")
    print("screen = script.capture_screen()")
    print("result = script.smart_find_image(screen, 'template.png')")

if __name__ == "__main__":
    main()

import win32con
import win32gui
import win32com.client

# 获取所有窗口句柄
hwnd_title = {}


def get_all_hwnd(hwnd, mouse):
    print('get all handle')
    if (win32gui.IsWindow(hwnd)
            and win32gui.IsWindowEnabled(hwnd)
            and win32gui.IsWindowVisible(hwnd)):
        hwnd_title.update({hwnd: win32gui.GetWindowText(hwnd)})


vindex = 0
win32gui.EnumWindows(get_all_hwnd, 0)
for h, t in hwnd_title.items():
    print('item: ', h, t)
    if "VMware" in t:
        print("置顶窗口")
        hwnd = h  # win32gui.FindWindow("vmware.exe", None)
        print(hwnd)
        if hwnd > 0:
            # hwnd = win32gui.FindWindow('xx.exe', None)
            # 窗口需要正常大小且在后台，不能最小化


            # 置顶窗口
            #win32gui.SetForegroundWindow(hwnd)
            win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
            # 窗口需要最大化且在后台，不能最小化
            # ctypes.windll.user32.ShowWindow(hwnd, 3)

            win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, vindex, vindex, 0, 0,
                                  win32con.SWP_NOZORDER |
                                  win32con.SWP_SHOWWINDOW | win32con.SWP_NOSIZE)

            vindex = vindex + 100

    # if t:
    # print('item: ',h, t)
    # 置顶窗口

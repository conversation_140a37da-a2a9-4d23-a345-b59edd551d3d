#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
透明模板搜索
处理带有背景色的模板图像，将背景色当作透明处理
"""

import cv2
import numpy as np
from image_color_script import ImageColorScript
from typing import List, Tuple, Optional, Union

class TransparentTemplateSearch:
    """支持透明背景的模板搜索"""
    
    def __init__(self):
        self.script = ImageColorScript()
    
    def detect_background_color(self, template: np.ndarray) -> Tuple[int, int, int]:
        """
        自动检测模板的背景色（通常是边角的颜色）
        
        Args:
            template: 模板图像
            
        Returns:
            背景色 (B, G, R)
        """
        h, w = template.shape[:2]
        
        # 采样四个角落的颜色
        corners = [
            template[0, 0],           # 左上角
            template[0, w-1],         # 右上角  
            template[h-1, 0],         # 左下角
            template[h-1, w-1]        # 右下角
        ]
        
        # 找出现最多的颜色作为背景色
        corner_colors = [tuple(corner) for corner in corners]
        color_counts = {}
        
        for color in corner_colors:
            color_counts[color] = color_counts.get(color, 0) + 1
        
        # 返回出现次数最多的颜色
        background_color = max(color_counts.items(), key=lambda x: x[1])[0]
        
        print(f"检测到背景色: {background_color}")
        return background_color
    
    def create_mask_from_background(self, template: np.ndarray, 
                                   background_color: Optional[Tuple[int, int, int]] = None,
                                   tolerance: int = 30) -> np.ndarray:
        """
        根据背景色创建掩码
        
        Args:
            template: 模板图像
            background_color: 背景色，None表示自动检测
            tolerance: 颜色容差
            
        Returns:
            掩码图像（255=前景，0=背景）
        """
        if background_color is None:
            background_color = self.detect_background_color(template)
        
        # 创建颜色范围
        lower_bound = np.array([max(0, c - tolerance) for c in background_color])
        upper_bound = np.array([min(255, c + tolerance) for c in background_color])
        
        # 创建掩码（背景为0，前景为255）
        mask = cv2.inRange(template, lower_bound, upper_bound)
        mask = 255 - mask  # 反转掩码
        
        return mask
    
    def template_match_with_mask(self, image: np.ndarray, template: np.ndarray, 
                                mask: np.ndarray) -> np.ndarray:
        """
        使用掩码进行模板匹配
        
        Args:
            image: 搜索图像
            template: 模板图像
            mask: 掩码图像
            
        Returns:
            匹配结果矩阵
        """
        # 使用带掩码的模板匹配
        result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED, mask=mask)
        return result
    
    def find_template_with_transparency(self, screen_img: np.ndarray, template_path: str,
                                       background_color: Optional[Tuple[int, int, int]] = None,
                                       tolerance: int = 30, threshold: float = 0.7,
                                       max_results: int = 10) -> List[Tuple[int, int, float]]:
        """
        使用透明背景进行模板搜索
        
        Args:
            screen_img: 屏幕图像
            template_path: 模板图像路径
            background_color: 背景色，None表示自动检测
            tolerance: 背景色容差
            threshold: 匹配阈值
            max_results: 最大结果数
            
        Returns:
            匹配结果列表 [(x, y, similarity), ...]
        """
        # 加载模板
        template = cv2.imread(template_path)
        if template is None:
            print(f"❌ 无法加载模板: {template_path}")
            return []
        
        print(f"🔍 使用透明背景搜索...")
        print(f"   模板尺寸: {template.shape}")
        
        # 如果指定了背景色，使用指定的；否则自动检测
        if background_color is not None:
            print(f"   使用指定背景色: {background_color}")
        else:
            background_color = self.detect_background_color(template)
        
        # 创建掩码
        mask = self.create_mask_from_background(template, background_color, tolerance)
        
        # 保存掩码用于调试
        cv2.imwrite("template_mask.png", mask)
        print(f"   掩码已保存: template_mask.png")
        
        # 转换为灰度图进行匹配
        gray_screen = cv2.cvtColor(screen_img, cv2.COLOR_BGR2GRAY)
        gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        
        # 使用掩码进行模板匹配
        result = self.template_match_with_mask(gray_screen, gray_template, mask)
        
        # 查找匹配位置
        locations = np.where(result >= threshold)
        
        matches = []
        for pt in zip(*locations[::-1]):
            x, y = pt
            similarity = float(result[y, x])
            matches.append((x, y, similarity))
        
        # 按相似度排序
        matches.sort(key=lambda x: x[2], reverse=True)
        
        # 非极大值抑制去重
        if len(matches) > 1:
            matches = self._nms_matches(matches, template.shape[1], template.shape[0])
        
        print(f"   找到 {len(matches)} 个匹配")
        
        return matches[:max_results]
    
    def _nms_matches(self, matches: List[Tuple[int, int, float]], 
                    template_w: int, template_h: int,
                    overlap_threshold: float = 0.3) -> List[Tuple[int, int, float]]:
        """非极大值抑制去重"""
        if len(matches) == 0:
            return []
        
        # 转换为边界框
        boxes = []
        scores = []
        for x, y, sim in matches:
            boxes.append([x, y, x + template_w, y + template_h])
            scores.append(sim)
        
        boxes = np.array(boxes)
        scores = np.array(scores)
        
        # NMS算法
        indices = np.argsort(scores)[::-1]
        keep = []
        
        while len(indices) > 0:
            current = indices[0]
            keep.append(current)
            
            if len(indices) == 1:
                break
            
            # 计算重叠
            current_box = boxes[current]
            other_boxes = boxes[indices[1:]]
            
            x1 = np.maximum(current_box[0], other_boxes[:, 0])
            y1 = np.maximum(current_box[1], other_boxes[:, 1])
            x2 = np.minimum(current_box[2], other_boxes[:, 2])
            y2 = np.minimum(current_box[3], other_boxes[:, 3])
            
            intersection = np.maximum(0, x2 - x1) * np.maximum(0, y2 - y1)
            current_area = (current_box[2] - current_box[0]) * (current_box[3] - current_box[1])
            other_areas = (other_boxes[:, 2] - other_boxes[:, 0]) * (other_boxes[:, 3] - other_boxes[:, 1])
            union = current_area + other_areas - intersection
            
            iou = intersection / union
            indices = indices[1:][iou <= overlap_threshold]
        
        return [matches[i] for i in keep]
    
    def search_with_purple_background(self, target_position: Tuple[int, int] = (262, 213)) -> List[Tuple[int, int, float]]:
        """
        专门针对紫色背景的搜索
        
        Args:
            target_position: 目标位置用于验证
            
        Returns:
            匹配结果
        """
        print("=== 紫色背景模板搜索 ===")
        print(f"目标位置: {target_position}")
        print()
        
        # 截取屏幕
        screen_img = self.script.capture_screen()
        print(f"屏幕尺寸: {screen_img.shape}")
        
        # 定义紫色背景色（根据您的图片）
        purple_background = (255, 0, 255)  # 紫色 (BGR格式)
        
        # 尝试不同的容差值
        tolerances = [10, 20, 30, 50]
        thresholds = [0.6, 0.7, 0.8]
        
        all_results = []
        
        for tolerance in tolerances:
            for threshold in thresholds:
                print(f"🔍 尝试参数: 容差={tolerance}, 阈值={threshold}")
                
                results = self.find_template_with_transparency(
                    screen_img,
                    "template.bmp",
                    background_color=purple_background,
                    tolerance=tolerance,
                    threshold=threshold,
                    max_results=20
                )
                
                if results:
                    print(f"   找到 {len(results)} 个候选")
                    
                    for i, (x, y, sim) in enumerate(results[:5]):
                        distance = abs(x - target_position[0]) + abs(y - target_position[1])
                        status = "🎯 很接近!" if distance < 10 else "✅ 接近" if distance < 30 else "❌ 较远"
                        print(f"     候选{i+1}: ({x}, {y}) 相似度={sim:.3f} 距离={distance}px {status}")
                    
                    all_results.extend(results)
                else:
                    print(f"   未找到匹配")
                
                print()
        
        # 去重并排序
        if all_results:
            # 简单去重（距离小于5px的认为是同一个）
            unique_results = []
            for result in all_results:
                is_duplicate = False
                for existing in unique_results:
                    if abs(result[0] - existing[0]) < 5 and abs(result[1] - existing[1]) < 5:
                        is_duplicate = True
                        break
                if not is_duplicate:
                    unique_results.append(result)
            
            # 按距离目标位置排序
            unique_results.sort(key=lambda r: abs(r[0] - target_position[0]) + abs(r[1] - target_position[1]))
            
            print(f"🏆 去重后的最佳结果:")
            for i, (x, y, sim) in enumerate(unique_results[:10]):
                distance = abs(x - target_position[0]) + abs(y - target_position[1])
                status = "🎯 很接近!" if distance < 10 else "✅ 接近" if distance < 30 else "❌ 较远"
                print(f"   结果{i+1}: ({x}, {y}) 相似度={sim:.3f} 距离={distance}px {status}")
            
            # 保存最佳结果
            if unique_results:
                best_result = unique_results[0]
                self.script.save_marked_image(
                    screen_img,
                    "transparent_search_result.png",
                    best_result,
                    "template.bmp",
                    color=(0, 255, 255),
                    show_info=True
                )
                print(f"\n💾 结果已保存: transparent_search_result.png")
            
            return unique_results
        
        return []

def main():
    """主函数"""
    print("=" * 60)
    print("透明背景模板搜索")
    print("=" * 60)
    print("专门处理带有紫色背景的模板图像")
    print("将紫色背景当作透明处理，只匹配前景内容")
    print("=" * 60)
    
    searcher = TransparentTemplateSearch()
    
    # 执行搜索
    results = searcher.search_with_purple_background(target_position=(262, 213))
    
    if results:
        best_result = results[0]
        x, y, sim = best_result
        distance = abs(x - 262) + abs(y - 213)
        
        if distance < 15:
            print(f"\n🎉 成功找到目标位置!")
            print(f"   位置: ({x}, {y})")
            print(f"   相似度: {sim:.3f}")
            print(f"   距离目标: {distance}px")
        else:
            print(f"\n⚠️ 找到候选位置，但可能不够精确")
            print(f"   最佳位置: ({x}, {y})")
            print(f"   距离目标: {distance}px")
    else:
        print(f"\n❌ 未找到匹配的位置")
        print("建议:")
        print("1. 检查模板图像是否正确")
        print("2. 确认紫色背景色值是否正确")
        print("3. 尝试调整容差和阈值参数")

if __name__ == "__main__":
    main()

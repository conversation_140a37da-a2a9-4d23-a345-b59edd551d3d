"""
银行应用UI界面演示
基于提供的UI设计图片创建的现代化银行应用界面
使用tkinter + customtkinter实现
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
from PIL import Image, ImageTk
import os

# 设置customtkinter主题
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class BankUIDemo:
    def __init__(self):
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("SxDx Bank - Banking Application")
        self.root.geometry("1400x900")
        self.root.configure(fg_color="#F8FAFC")
        
        # 设置窗口图标和基本属性
        self.root.resizable(True, True)
        self.root.minsize(1200, 800)
        
        # 初始化UI组件
        self.setup_main_layout()
        
    def setup_main_layout(self):
        """设置主要布局结构"""
        # 创建主容器
        self.main_container = ctk.CTkFrame(self.root, fg_color="transparent")
        self.main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 配置网格权重
        self.main_container.grid_columnconfigure(1, weight=1)
        self.main_container.grid_rowconfigure(0, weight=1)
        
        # 创建左侧导航栏
        self.create_sidebar()
        
        # 创建主内容区域
        self.create_main_content()
        
        # 创建右侧详情面板
        self.create_right_panel()
        
    def create_sidebar(self):
        """创建左侧导航栏"""
        self.sidebar = ctk.CTkFrame(
            self.main_container,
            width=250,
            fg_color="#FFFFFF",
            corner_radius=15
        )
        self.sidebar.grid(row=0, column=0, sticky="nsew", padx=(0, 15))
        self.sidebar.grid_propagate(False)
        
        # Logo区域
        logo_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        logo_frame.pack(fill="x", padx=20, pady=(20, 30))
        
        # 银行Logo和名称
        logo_label = ctk.CTkLabel(
            logo_frame,
            text="🏦 SxDx Bank",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color="#8B5CF6"
        )
        logo_label.pack(anchor="w")
        
        # 菜单标题
        menu_title = ctk.CTkLabel(
            self.sidebar,
            text="Menu",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#6B7280"
        )
        menu_title.pack(anchor="w", padx=20, pady=(0, 15))
        
        # 菜单项
        menu_items = [
            ("🏠", "Home", True),
            ("💳", "Transactions", False),
            ("📄", "Billings", False),
            ("👥", "Contacts", False),
            ("👤", "Clients", False),
            ("⚙️", "Settings", False)
        ]
        
        for icon, text, is_active in menu_items:
            self.create_menu_item(icon, text, is_active)
            
        # Help Center按钮（底部）
        help_frame = ctk.CTkFrame(
            self.sidebar,
            fg_color="#8B5CF6",
            corner_radius=15,
            height=120
        )
        help_frame.pack(side="bottom", fill="x", padx=20, pady=20)
        help_frame.pack_propagate(False)
        
        help_icon = ctk.CTkLabel(
            help_frame,
            text="❓",
            font=ctk.CTkFont(size=24),
            text_color="white"
        )
        help_icon.pack(pady=(15, 5))
        
        help_title = ctk.CTkLabel(
            help_frame,
            text="Help Center",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="white"
        )
        help_title.pack()
        
        help_subtitle = ctk.CTkLabel(
            help_frame,
            text="Having trouble in Figma?\nContact us for help",
            font=ctk.CTkFont(size=10),
            text_color="white",
            justify="center"
        )
        help_subtitle.pack(pady=(5, 10))
        
        help_button = ctk.CTkButton(
            help_frame,
            text="Go To Help Center",
            font=ctk.CTkFont(size=10),
            fg_color="white",
            text_color="#8B5CF6",
            height=25,
            corner_radius=8
        )
        help_button.pack(pady=(0, 10))
        
    def create_menu_item(self, icon, text, is_active=False):
        """创建菜单项"""
        bg_color = "#F3F4F6" if is_active else "transparent"
        text_color = "#8B5CF6" if is_active else "#6B7280"
        
        item_frame = ctk.CTkFrame(
            self.sidebar,
            fg_color=bg_color,
            corner_radius=10,
            height=45
        )
        item_frame.pack(fill="x", padx=20, pady=2)
        item_frame.pack_propagate(False)
        
        # 图标
        icon_label = ctk.CTkLabel(
            item_frame,
            text=icon,
            font=ctk.CTkFont(size=16),
            text_color=text_color,
            width=30
        )
        icon_label.pack(side="left", padx=(15, 10), pady=12)
        
        # 文字
        text_label = ctk.CTkLabel(
            item_frame,
            text=text,
            font=ctk.CTkFont(size=14),
            text_color=text_color
        )
        text_label.pack(side="left", pady=12)
        
    def create_main_content(self):
        """创建主内容区域"""
        self.main_content = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        self.main_content.grid(row=0, column=1, sticky="nsew", padx=(0, 15))
        
        # 顶部栏（欢迎信息和用户头像）
        top_bar = ctk.CTkFrame(self.main_content, fg_color="transparent")
        top_bar.pack(fill="x", pady=(0, 20))

        # 欢迎信息
        welcome_frame = ctk.CTkFrame(top_bar, fg_color="transparent")
        welcome_frame.pack(side="left", fill="both", expand=True)

        welcome_title = ctk.CTkLabel(
            welcome_frame,
            text="Welcome to SxDx.",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color="#1F2937"
        )
        welcome_title.pack(anchor="w")

        welcome_subtitle = ctk.CTkLabel(
            welcome_frame,
            text="Hello Shakir, welcome back!",
            font=ctk.CTkFont(size=14),
            text_color="#6B7280"
        )
        welcome_subtitle.pack(anchor="w")

        # 右上角用户区域
        user_frame = ctk.CTkFrame(top_bar, fg_color="transparent")
        user_frame.pack(side="right")

        # 通知图标
        notification_btn = ctk.CTkButton(
            user_frame,
            text="🔔",
            width=40,
            height=40,
            fg_color="#F3F4F6",
            text_color="#6B7280",
            hover_color="#E5E7EB",
            corner_radius=10,
            font=ctk.CTkFont(size=16)
        )
        notification_btn.pack(side="left", padx=(0, 10))

        # 消息图标
        message_btn = ctk.CTkButton(
            user_frame,
            text="💬",
            width=40,
            height=40,
            fg_color="#F3F4F6",
            text_color="#6B7280",
            hover_color="#E5E7EB",
            corner_radius=10,
            font=ctk.CTkFont(size=16)
        )
        message_btn.pack(side="left", padx=(0, 10))

        # 用户头像
        avatar_btn = ctk.CTkButton(
            user_frame,
            text="👤",
            width=40,
            height=40,
            fg_color="#FF6B35",
            text_color="white",
            hover_color="#E55A2B",
            corner_radius=20,
            font=ctk.CTkFont(size=16)
        )
        avatar_btn.pack(side="left")
        
        # 内容区域容器
        content_container = ctk.CTkFrame(self.main_content, fg_color="transparent")
        content_container.pack(fill="both", expand=True)
        
        # 配置网格
        content_container.grid_columnconfigure(0, weight=1)
        content_container.grid_columnconfigure(1, weight=1)
        content_container.grid_rowconfigure(1, weight=1)
        
        # 信用卡区域
        self.create_credit_card_section(content_container)
        
        # 交易记录区域
        self.create_transactions_section(content_container)
        
    def create_credit_card_section(self, parent):
        """创建信用卡展示区域"""
        card_frame = ctk.CTkFrame(parent, fg_color="transparent")
        card_frame.grid(row=0, column=0, sticky="ew", padx=(0, 10), pady=(0, 20))

        # 信用卡标题
        card_title = ctk.CTkLabel(
            card_frame,
            text="Credit Card",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color="#1F2937"
        )
        card_title.pack(anchor="w", pady=(0, 15))

        # 信用卡主体
        self.create_credit_card(card_frame)

        # 卡片信息区域
        card_info_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        card_info_frame.pack(fill="x", pady=(15, 0))

        # 支付限额信息
        limit_frame = ctk.CTkFrame(card_info_frame, fg_color="transparent")
        limit_frame.pack(fill="x", pady=(0, 10))

        limit_label = ctk.CTkLabel(
            limit_frame,
            text="Internet Payment Limit",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        limit_label.pack(anchor="w")

        limit_value = ctk.CTkLabel(
            limit_frame,
            text="$200 / $500",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#1F2937"
        )
        limit_value.pack(anchor="w")

        # 进度条
        progress_bar = ctk.CTkProgressBar(
            limit_frame,
            width=300,
            height=8,
            progress_color="#8B5CF6",
            fg_color="#E5E7EB"
        )
        progress_bar.pack(anchor="w", pady=(5, 0))
        progress_bar.set(0.4)  # 40% 进度

        # 底部按钮区域
        button_frame = ctk.CTkFrame(card_info_frame, fg_color="transparent")
        button_frame.pack(fill="x", pady=(15, 0))

        # 创建新卡按钮
        new_card_btn = ctk.CTkButton(
            button_frame,
            text="Create New Card",
            font=ctk.CTkFont(size=12),
            fg_color="#F3F4F6",
            text_color="#6B7280",
            hover_color="#E5E7EB",
            corner_radius=8,
            height=35,
            width=140
        )
        new_card_btn.pack(side="left", padx=(0, 10))

        # 订阅信息
        subscription_label = ctk.CTkLabel(
            button_frame,
            text="13 active subscriptions",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        subscription_label.pack(side="left", pady=8)

    def create_credit_card(self, parent):
        """创建信用卡组件"""
        # 信用卡容器
        card_container = ctk.CTkFrame(
            parent,
            height=200,
            fg_color="#8B5CF6",  # 紫色背景
            corner_radius=15
        )
        card_container.pack(fill="x")
        card_container.pack_propagate(False)

        # 卡片顶部区域
        card_top = ctk.CTkFrame(card_container, fg_color="transparent")
        card_top.pack(fill="x", padx=20, pady=(20, 10))

        # 芯片图标
        chip_label = ctk.CTkLabel(
            card_top,
            text="💳",
            font=ctk.CTkFont(size=24),
            text_color="white"
        )
        chip_label.pack(side="left")

        # 卡片类型
        card_type = ctk.CTkLabel(
            card_top,
            text="AR Shakir",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="white"
        )
        card_type.pack(side="right")

        # 卡号区域
        card_number_frame = ctk.CTkFrame(card_container, fg_color="transparent")
        card_number_frame.pack(fill="x", padx=20, pady=(20, 0))

        card_number = ctk.CTkLabel(
            card_number_frame,
            text="3829 4820 4629 5025",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="white"
        )
        card_number.pack(anchor="w")

        # 卡片底部信息
        card_bottom = ctk.CTkFrame(card_container, fg_color="transparent")
        card_bottom.pack(fill="x", padx=20, pady=(15, 20))

        # 持卡人姓名
        cardholder_label = ctk.CTkLabel(
            card_bottom,
            text="Card Holder",
            font=ctk.CTkFont(size=10),
            text_color="#E5E7EB"
        )
        cardholder_label.pack(side="left", anchor="sw")

        cardholder_name = ctk.CTkLabel(
            card_bottom,
            text="AR Shakir",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="white"
        )
        cardholder_name.pack(side="left", anchor="nw", padx=(0, 50))

        # 有效期
        expiry_label = ctk.CTkLabel(
            card_bottom,
            text="Valid Thru",
            font=ctk.CTkFont(size=10),
            text_color="#E5E7EB"
        )
        expiry_label.pack(side="left", anchor="sw")

        expiry_date = ctk.CTkLabel(
            card_bottom,
            text="09/17",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="white"
        )
        expiry_date.pack(side="left", anchor="nw")
        
    def create_transactions_section(self, parent):
        """创建交易记录区域"""
        trans_frame = ctk.CTkFrame(parent, fg_color="transparent")
        trans_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", pady=(10, 0))

        # 标题栏
        title_frame = ctk.CTkFrame(trans_frame, fg_color="transparent")
        title_frame.pack(fill="x", pady=(0, 15))

        # 交易记录标题
        trans_title = ctk.CTkLabel(
            title_frame,
            text="All Transactions",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color="#1F2937"
        )
        trans_title.pack(side="left")

        # 导航箭头
        nav_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        nav_frame.pack(side="right")

        left_arrow = ctk.CTkButton(
            nav_frame,
            text="←",
            width=30,
            height=30,
            fg_color="#F3F4F6",
            text_color="#6B7280",
            hover_color="#E5E7EB",
            corner_radius=8,
            font=ctk.CTkFont(size=14)
        )
        left_arrow.pack(side="left", padx=(0, 5))

        right_arrow = ctk.CTkButton(
            nav_frame,
            text="→",
            width=30,
            height=30,
            fg_color="#F3F4F6",
            text_color="#6B7280",
            hover_color="#E5E7EB",
            corner_radius=8,
            font=ctk.CTkFont(size=14)
        )
        right_arrow.pack(side="left")

        # 交易记录容器
        trans_container = ctk.CTkFrame(
            trans_frame,
            fg_color="#FFFFFF",
            corner_radius=15
        )
        trans_container.pack(fill="both", expand=True)

        # 周汇总区域
        self.create_week_summary(trans_container)

        # 交易列表
        self.create_transaction_list(trans_container)

        # 底部交易详情
        self.create_bottom_transactions(trans_container)

    def create_week_summary(self, parent):
        """创建周汇总区域"""
        summary_frame = ctk.CTkFrame(parent, fg_color="transparent")
        summary_frame.pack(fill="x", padx=20, pady=20)

        # 本周汇总标题
        summary_title = ctk.CTkLabel(
            summary_frame,
            text="This Week Summary",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        summary_title.pack(anchor="w")

        # 金额显示
        amount_frame = ctk.CTkFrame(summary_frame, fg_color="transparent")
        amount_frame.pack(fill="x", pady=(5, 0))

        # 主要金额
        main_amount = ctk.CTkLabel(
            amount_frame,
            text="💰 113,650 PKR",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="#1F2937"
        )
        main_amount.pack(side="left")

        # 统计信息
        stats_frame = ctk.CTkFrame(amount_frame, fg_color="transparent")
        stats_frame.pack(side="right")

        income_label = ctk.CTkLabel(
            stats_frame,
            text="26,000 PKR",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        income_label.pack(side="left", padx=(0, 20))

        expense_label = ctk.CTkLabel(
            stats_frame,
            text="5,324 PKR",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        expense_label.pack(side="left")

    def create_transaction_list(self, parent):
        """创建交易列表"""
        list_frame = ctk.CTkFrame(parent, fg_color="transparent")
        list_frame.pack(fill="x", padx=20, pady=(0, 20))

        # 交易项目数据
        transactions = [
            ("Hulu", "Hulu Subscription", "Streaming Services", "-3,100 PKR", "#FF6B6B", "📺"),
            ("YouTube", "Youtube Payout", "Content Creation", "+20,000 PKR", "#4ECDC4", "📹"),
            ("College", "College Fee", "Personal Expenses", "-1,390 PKR", "#45B7D1", "🎓")
        ]

        for company, description, category, amount, color, icon in transactions:
            self.create_transaction_item(list_frame, company, description, category, amount, color, icon)

        # 查看全部按钮
        see_all_btn = ctk.CTkButton(
            list_frame,
            text="See All",
            font=ctk.CTkFont(size=12),
            fg_color="transparent",
            text_color="#8B5CF6",
            hover_color="#F3F4F6",
            height=30
        )
        see_all_btn.pack(anchor="e", pady=(10, 0))

    def create_transaction_item(self, parent, company, description, category, amount, color, icon):
        """创建单个交易项目"""
        item_frame = ctk.CTkFrame(parent, fg_color="transparent", height=60)
        item_frame.pack(fill="x", pady=5)
        item_frame.pack_propagate(False)

        # 图标区域
        icon_frame = ctk.CTkFrame(
            item_frame,
            width=45,
            height=45,
            fg_color=color,
            corner_radius=12
        )
        icon_frame.pack(side="left", padx=(0, 15), pady=7)
        icon_frame.pack_propagate(False)

        icon_label = ctk.CTkLabel(
            icon_frame,
            text=icon,
            font=ctk.CTkFont(size=20),
            text_color="white"
        )
        icon_label.pack(expand=True)

        # 信息区域
        info_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
        info_frame.pack(side="left", fill="both", expand=True, pady=7)

        # 公司名称
        company_label = ctk.CTkLabel(
            info_frame,
            text=company,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#1F2937"
        )
        company_label.pack(anchor="w")

        # 描述
        desc_label = ctk.CTkLabel(
            info_frame,
            text=description,
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        desc_label.pack(anchor="w")

        # 分类
        category_label = ctk.CTkLabel(
            info_frame,
            text=category,
            font=ctk.CTkFont(size=10),
            text_color="#9CA3AF"
        )
        category_label.pack(anchor="w")

        # 金额和操作按钮
        right_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
        right_frame.pack(side="right", pady=7)

        # 金额
        amount_label = ctk.CTkLabel(
            right_frame,
            text=amount,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#1F2937"
        )
        amount_label.pack(anchor="e")

        # 操作按钮
        action_btn = ctk.CTkButton(
            right_frame,
            text="⋯",
            width=30,
            height=25,
            fg_color="transparent",
            text_color="#6B7280",
            hover_color="#F3F4F6",
            font=ctk.CTkFont(size=16)
        )
        action_btn.pack(anchor="e", pady=(5, 0))

    def create_bottom_transactions(self, parent):
        """创建底部交易详情"""
        bottom_frame = ctk.CTkFrame(parent, fg_color="transparent")
        bottom_frame.pack(fill="x", padx=20, pady=(0, 20))

        # 分隔线
        separator = ctk.CTkFrame(bottom_frame, height=1, fg_color="#E5E7EB")
        separator.pack(fill="x", pady=(0, 15))

        # 日期标题
        date_label = ctk.CTkLabel(
            bottom_frame,
            text="Mon, Mar 1",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#1F2937"
        )
        date_label.pack(anchor="w", pady=(0, 15))

        # 底部交易列表
        bottom_transactions = [
            ("💸", "Payment to Nick", "$560.00", False),
            ("💰", "Receive Salary", "$12,200.00", False),
            ("📤", "Moneygram Transfer", "$3,000.00", True),
            ("💰", "Receive Refund", "$1,550.00", False)
        ]

        for icon, description, amount, is_active in bottom_transactions:
            self.create_bottom_transaction_item(bottom_frame, icon, description, amount, is_active)

    def create_bottom_transaction_item(self, parent, icon, description, amount, is_active=False):
        """创建底部交易项目"""
        item_frame = ctk.CTkFrame(parent, fg_color="transparent", height=50)
        item_frame.pack(fill="x", pady=2)
        item_frame.pack_propagate(False)

        # 图标
        icon_label = ctk.CTkLabel(
            item_frame,
            text=icon,
            font=ctk.CTkFont(size=16),
            text_color="#1F2937",
            width=30
        )
        icon_label.pack(side="left", padx=(0, 15), pady=15)

        # 描述
        desc_label = ctk.CTkLabel(
            item_frame,
            text=description,
            font=ctk.CTkFont(size=14),
            text_color="#1F2937"
        )
        desc_label.pack(side="left", pady=15)

        # 播放按钮（如果是活跃状态）
        if is_active:
            play_btn = ctk.CTkButton(
                item_frame,
                text="▶",
                width=25,
                height=25,
                fg_color="#8B5CF6",
                text_color="white",
                corner_radius=12,
                font=ctk.CTkFont(size=12)
            )
            play_btn.pack(side="left", padx=(10, 0), pady=15)

        # 金额
        amount_label = ctk.CTkLabel(
            item_frame,
            text=amount,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#1F2937"
        )
        amount_label.pack(side="right", pady=15)
        
    def create_right_panel(self):
        """创建右侧详情面板"""
        self.right_panel = ctk.CTkFrame(
            self.main_container,
            width=350,
            fg_color="#FFFFFF",
            corner_radius=15
        )
        self.right_panel.grid(row=0, column=2, sticky="nsew")
        self.right_panel.grid_propagate(False)

        # 标题栏
        title_frame = ctk.CTkFrame(self.right_panel, fg_color="transparent")
        title_frame.pack(fill="x", padx=20, pady=20)

        panel_title = ctk.CTkLabel(
            title_frame,
            text="Payment Details",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="#1F2937"
        )
        panel_title.pack(side="left")

        # 更多选项按钮
        more_btn = ctk.CTkButton(
            title_frame,
            text="⋯",
            width=30,
            height=30,
            fg_color="transparent",
            text_color="#6B7280",
            hover_color="#F3F4F6",
            font=ctk.CTkFont(size=16)
        )
        more_btn.pack(side="right")

        # 支付信息区域
        self.create_payment_info()

        # 操作按钮区域
        self.create_action_buttons()

    def create_payment_info(self):
        """创建支付信息区域"""
        info_frame = ctk.CTkFrame(self.right_panel, fg_color="transparent")
        info_frame.pack(fill="x", padx=20, pady=(0, 30))

        # From 信息
        from_label = ctk.CTkLabel(
            info_frame,
            text="From",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        from_label.pack(anchor="w", pady=(0, 5))

        from_value = ctk.CTkLabel(
            info_frame,
            text="Habib Bank Limited",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#1F2937"
        )
        from_value.pack(anchor="w", pady=(0, 20))

        # 金额显示
        amount_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        amount_frame.pack(fill="x", pady=(0, 20))

        amount_label = ctk.CTkLabel(
            amount_frame,
            text="+1,550.00",
            font=ctk.CTkFont(size=32, weight="bold"),
            text_color="#1F2937"
        )
        amount_label.pack(side="left")

        # 类型和分类信息
        type_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        type_frame.pack(fill="x")

        # Type
        type_label = ctk.CTkLabel(
            type_frame,
            text="Type",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        type_label.pack(anchor="w")

        type_value = ctk.CTkLabel(
            type_frame,
            text="Cash Refund",
            font=ctk.CTkFont(size=14),
            text_color="#1F2937"
        )
        type_value.pack(anchor="w", pady=(2, 15))

        # Category
        category_label = ctk.CTkLabel(
            type_frame,
            text="Category",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        category_label.pack(anchor="w")

        category_value = ctk.CTkLabel(
            type_frame,
            text="Annual Cashback",
            font=ctk.CTkFont(size=14),
            text_color="#1F2937"
        )
        category_value.pack(anchor="w", pady=(2, 15))

        # 时间信息
        time_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        time_frame.pack(fill="x")

        time_value = ctk.CTkLabel(
            time_frame,
            text="1:48 PM",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        time_value.pack(side="right")

        date_value = ctk.CTkLabel(
            time_frame,
            text="Mon, Mar 1",
            font=ctk.CTkFont(size=12),
            text_color="#6B7280"
        )
        date_value.pack(side="right", padx=(0, 20))

    def create_action_buttons(self):
        """创建操作按钮区域"""
        button_frame = ctk.CTkFrame(self.right_panel, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=(0, 20))

        # 按钮容器
        btn_container = ctk.CTkFrame(button_frame, fg_color="transparent")
        btn_container.pack(fill="x")

        # Manage 按钮
        manage_btn = ctk.CTkButton(
            btn_container,
            text="Manage",
            font=ctk.CTkFont(size=14),
            fg_color="#F3F4F6",
            text_color="#6B7280",
            hover_color="#E5E7EB",
            corner_radius=8,
            height=40,
            width=100
        )
        manage_btn.pack(side="left", padx=(0, 10))

        # Invoice 按钮
        invoice_btn = ctk.CTkButton(
            btn_container,
            text="Invoice",
            font=ctk.CTkFont(size=14),
            fg_color="#F3F4F6",
            text_color="#6B7280",
            hover_color="#E5E7EB",
            corner_radius=8,
            height=40,
            width=100
        )
        invoice_btn.pack(side="left", padx=(0, 10))

        # Add as a Recipient 按钮
        recipient_btn = ctk.CTkButton(
            btn_container,
            text="Add as a Recipient",
            font=ctk.CTkFont(size=14),
            fg_color="#8B5CF6",
            text_color="white",
            hover_color="#7C3AED",
            corner_radius=8,
            height=40
        )
        recipient_btn.pack(fill="x", pady=(10, 0))
        
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    # 检查是否安装了customtkinter
    try:
        import customtkinter
    except ImportError:
        print("请先安装customtkinter: pip install customtkinter")
        exit(1)
        
    app = BankUIDemo()
    app.run()

# 银行应用UI界面演示

## 项目简介

这是一个基于提供的UI设计图片创建的现代化银行应用界面演示程序。使用Python的tkinter和customtkinter库实现，完全还原了原始设计的视觉效果和布局结构。

## 功能特点

### 🎨 设计特色
- **现代化UI设计**：采用紫色渐变主题，简洁优雅
- **响应式布局**：三栏布局设计，适配不同屏幕尺寸
- **圆角卡片**：所有组件采用圆角设计，视觉效果更佳
- **渐变效果**：信用卡组件采用紫色渐变背景

### 🏗️ 界面结构

#### 左侧导航栏
- **Logo区域**：SxDx Bank品牌标识
- **菜单项**：
  - 🏠 Home（当前激活）
  - 💳 Transactions
  - 📄 Billings
  - 👥 Contacts
  - 👤 Clients
  - ⚙️ Settings
- **Help Center**：紫色渐变帮助中心卡片

#### 主内容区域
- **欢迎信息**：个性化欢迎消息
- **用户操作栏**：通知、消息、用户头像
- **信用卡展示**：
  - 紫色渐变卡片设计
  - 卡号、持卡人、有效期信息
  - 支付限额进度条
  - 创建新卡和订阅信息
- **交易记录列表**：
  - 周汇总信息
  - 交易项目详情（图标、描述、金额）
  - 底部历史交易记录

#### 右侧详情面板
- **支付详情**：详细的支付信息展示
- **操作按钮**：Manage、Invoice、Add as Recipient

## 技术实现

### 依赖库
```bash
pip install customtkinter
```

### 核心技术
- **tkinter**：Python内置GUI框架
- **customtkinter**：现代化UI组件库
- **PIL (Pillow)**：图像处理支持

### 文件结构
```
bank_ui_demo.py          # 主程序文件
README_bank_ui.md        # 说明文档
```

## 运行方法

1. **安装依赖**：
   ```bash
   pip install customtkinter
   ```

2. **运行程序**：
   ```bash
   python bank_ui_demo.py
   ```

## 界面预览

程序运行后将显示完整的银行应用界面，包括：
- 左侧导航菜单
- 中央信用卡和交易记录区域
- 右侧支付详情面板

## 设计亮点

### 🎯 色彩方案
- **主色调**：紫色渐变（#8B5CF6 到 #A855F7）
- **背景色**：浅灰色（#F8FAFC）
- **卡片背景**：纯白色（#FFFFFF）
- **文字颜色**：深灰色系，层次分明

### 🔧 交互设计
- **悬停效果**：按钮和可点击元素的悬停状态
- **状态指示**：当前激活菜单项的高亮显示
- **进度展示**：支付限额的可视化进度条

### 📱 响应式特性
- **灵活布局**：支持窗口大小调整
- **最小尺寸**：1200x800像素最小窗口限制
- **比例协调**：各区域比例自适应

## 代码结构

### 主要类和方法
- `BankUIDemo`：主应用类
- `setup_main_layout()`：主布局设置
- `create_sidebar()`：左侧导航栏
- `create_main_content()`：主内容区域
- `create_credit_card()`：信用卡组件
- `create_transaction_list()`：交易列表
- `create_right_panel()`：右侧详情面板

### 组件化设计
每个UI区域都被封装为独立的方法，便于维护和扩展。

## 扩展建议

1. **功能实现**：为按钮添加实际的事件处理
2. **数据绑定**：连接真实的银行数据API
3. **动画效果**：添加页面切换和交互动画
4. **主题切换**：支持明暗主题切换
5. **国际化**：支持多语言界面

## 注意事项

- 确保已安装customtkinter库
- 程序仅为UI演示，不包含实际银行功能
- 建议在1400x900或更大分辨率下运行以获得最佳体验

---

**开发说明**：此界面完全基于提供的UI设计图片创建，力求最大程度还原原始设计的视觉效果和用户体验。

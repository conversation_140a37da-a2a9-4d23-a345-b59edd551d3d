#!/usr/bin/env python3
"""
字库制作UI界面
为zhaoTuZhaoSe.py提供图形化界面
"""

try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext, simpledialog
    from PIL import Image, ImageTk
    import threading
    UI_AVAILABLE = True
except ImportError as e:
    print(f"UI依赖库未安装: {e}")
    print("请安装: pip install pillow")
    UI_AVAILABLE = False

import os
import sys
import cv2
import numpy as np
from datetime import datetime

# 导入主功能模块
from zhaoTuZhaoSe import FontLibraryCreator, FontLibraryManager, ImageColorScript

class FontLibraryUI:
    """
    字库制作UI界面类
    提供图形化界面来制作、管理字库和屏幕找字功能
    """
    
    def __init__(self):
        """初始化UI界面"""
        if not UI_AVAILABLE:
            raise ImportError("UI依赖库未安装，请安装: pip install pillow")
            
        self.root = tk.Tk()
        self.root.title("字库制作工具 - zhaoTuZhaoSe")

        # 设置窗口大小并居中显示
        window_width = 1200
        window_height = 800

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置
        center_x = int(screen_width/2 - window_width/2)
        center_y = int(screen_height/2 - window_height/2)

        # 设置窗口位置和大小
        self.root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")
        self.root.minsize(1000, 600)
        
        # 初始化功能类实例
        self.font_creator = FontLibraryCreator()
        self.font_manager = FontLibraryManager()
        self.image_script = ImageColorScript()
        
        # 当前选中的功能模块
        self.current_module = "create"
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_main_interface()
        
        # 状态变量
        self.loaded_libraries = {}  # 已加载的字库
        
    def setup_styles(self):
        """设置UI样式"""
        self.style = ttk.Style()
        
        # 使用现代主题
        available_themes = self.style.theme_names()
        if 'vista' in available_themes:
            self.style.theme_use('vista')
        elif 'winnative' in available_themes:
            self.style.theme_use('winnative')
        else:
            self.style.theme_use('clam')
        
        # 定义现代化按钮样式 - 参考button_style_demo.py
        # Plain button style (white background, gray text and border)
        self.style.configure("Plain.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#6B7280',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Plain.TButton",
                     background=[('active', '#F9FAFB'),
                               ('pressed', '#F3F4F6'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#6B7280'),
                                ('!focus', '#6B7280'),
                                ('disabled', '#E5E7EB')])

        # Primary button style (white background, blue text and border)
        self.style.configure("Primary.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#3B82F6',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Primary.TButton",
                     background=[('active', '#EBF4FF'),
                               ('pressed', '#DBEAFE'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#3B82F6'),
                                ('!focus', '#3B82F6'),
                                ('disabled', '#E5E7EB')])

        # Success button style (white background, green text and border)
        self.style.configure("Success.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#10B981',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Success.TButton",
                     background=[('active', '#ECFDF5'),
                               ('pressed', '#D1FAE5'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#10B981'),
                                ('!focus', '#10B981'),
                                ('disabled', '#E5E7EB')])

        # Warning button style (white background, orange text and border)
        self.style.configure("Warning.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#F59E0B',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Warning.TButton",
                     background=[('active', '#FFFBEB'),
                               ('pressed', '#FEF3C7'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#F59E0B'),
                                ('!focus', '#F59E0B'),
                                ('disabled', '#E5E7EB')])

        # Danger button style (white background, red text and border)
        self.style.configure("Danger.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#EF4444',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Danger.TButton",
                     background=[('active', '#FEF2F2'),
                               ('pressed', '#FECACA'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#EF4444'),
                                ('!focus', '#EF4444'),
                                ('disabled', '#E5E7EB')])
    
    def create_main_interface(self):
        """创建主界面"""
        # 创建主容器
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        title_label = ttk.Label(main_frame, text="🔤 字库制作工具", 
                               font=('Segoe UI', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 创建导航栏
        self.create_navigation(main_frame)
        
        # 创建主内容区域
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # 创建状态栏
        self.create_status_bar(main_frame)
        
        # 默认显示制作字库界面
        self.show_create_module()
    
    def create_navigation(self, parent):
        """创建导航栏"""
        nav_frame = ttk.Frame(parent)
        nav_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 导航按钮
        nav_buttons = [
            ("📝 制作字库", "create", "Primary.TButton"),
            ("📚 管理字库", "manage", "Success.TButton"),
            ("🔍 屏幕找字", "search", "Warning.TButton")
        ]
        
        for text, module, style in nav_buttons:
            btn = ttk.Button(nav_frame, text=text, style=style,
                           command=lambda m=module: self.switch_module(m))
            btn.pack(side=tk.LEFT, padx=(0, 10))
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, text="就绪", 
                                     font=('Segoe UI', 9))
        self.status_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
    
    def switch_module(self, module):
        """切换功能模块"""
        if self.current_module == module:
            return
            
        self.current_module = module
        
        # 清空当前内容
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # 显示对应模块
        if module == "create":
            self.show_create_module()
        elif module == "manage":
            self.show_manage_module()
        elif module == "search":
            self.show_search_module()
    
    def update_status(self, message, show_progress=False):
        """更新状态栏"""
        self.status_label.config(text=message)
        if show_progress:
            self.progress_bar.start()
        else:
            self.progress_bar.stop()
        self.root.update_idletasks()
    
    def handle_error(self, title, message):
        """处理错误"""
        self.update_status("操作失败")
        messagebox.showerror(title, message)
        print(f"错误: {title} - {message}")  # 同时输出到控制台用于调试

    # ==================== 制作字库模块 ====================

    def show_create_module(self):
        """显示制作字库模块"""
        # 创建主容器
        create_frame = ttk.Frame(self.content_frame)
        create_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧控制面板
        left_frame = ttk.LabelFrame(create_frame, text="字库制作设置", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.config(width=350)
        left_frame.pack_propagate(False)

        # 图像文件选择
        file_frame = ttk.Frame(left_frame)
        file_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(file_frame, text="选择图像文件:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, pady=(5, 0))

        self.image_path_var = tk.StringVar()
        self.image_path_entry = ttk.Entry(file_select_frame, textvariable=self.image_path_var,
                                         state='readonly')
        self.image_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(file_select_frame, text="浏览", style="Plain.TButton",
                  command=self.select_image_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 文字内容输入
        text_frame = ttk.Frame(left_frame)
        text_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(text_frame, text="图像中的文字内容:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.text_content_var = tk.StringVar()
        self.text_content_entry = ttk.Entry(text_frame, textvariable=self.text_content_var)
        self.text_content_entry.pack(fill=tk.X, pady=(5, 0))

        # 字库信息设置
        info_frame = ttk.Frame(left_frame)
        info_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(info_frame, text="字库信息:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # 字库名称
        name_frame = ttk.Frame(info_frame)
        name_frame.pack(fill=tk.X, pady=(5, 5))
        ttk.Label(name_frame, text="名称:").pack(side=tk.LEFT)
        self.library_name_var = tk.StringVar(value="新字库")
        ttk.Entry(name_frame, textvariable=self.library_name_var, width=20).pack(side=tk.RIGHT)

        # 字库描述
        desc_frame = ttk.Frame(info_frame)
        desc_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(desc_frame, text="描述:").pack(side=tk.LEFT)
        self.library_desc_var = tk.StringVar()
        ttk.Entry(desc_frame, textvariable=self.library_desc_var, width=20).pack(side=tk.RIGHT)

        # 操作按钮
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(button_frame, text="预览分割", style="Primary.TButton",
                  command=self.preview_segmentation).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="制作字库", style="Success.TButton",
                  command=self.create_font_library).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="保存字库", style="Warning.TButton",
                  command=self.save_font_library).pack(fill=tk.X)

        # 右侧预览区域
        right_frame = ttk.LabelFrame(create_frame, text="预览区域", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建滚动区域
        canvas = tk.Canvas(right_frame, bg='white')
        scrollbar_v = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=canvas.yview)
        scrollbar_h = ttk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=canvas.xview)

        self.preview_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        canvas.create_window((0, 0), window=self.preview_frame, anchor=tk.NW)

        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 绑定滚动事件
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        self.preview_frame.bind("<Configure>", configure_scroll_region)

        # 初始显示提示
        self.show_preview_hint()

    def show_preview_hint(self):
        """显示预览提示"""
        hint_label = ttk.Label(self.preview_frame,
                              text="请选择图像文件并输入文字内容，然后点击'预览分割'查看字符分割效果",
                              font=('Segoe UI', 11),
                              foreground='#6B7280')
        hint_label.pack(expand=True, pady=50)

    def select_image_file(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[
                ("图像文件", "*.png *.jpg *.jpeg *.bmp *.tiff"),
                ("PNG文件", "*.png"),
                ("JPEG文件", "*.jpg *.jpeg"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.image_path_var.set(file_path)
            self.update_status(f"已选择图像: {os.path.basename(file_path)}")

    def preview_segmentation(self):
        """预览字符分割"""
        image_path = self.image_path_var.get()
        text_content = self.text_content_var.get().strip()

        if not image_path:
            messagebox.showwarning("警告", "请先选择图像文件")
            return

        if not text_content:
            messagebox.showwarning("警告", "请输入图像中的文字内容")
            return

        try:
            self.update_status("正在分割字符...", True)

            # 清空预览区域
            for widget in self.preview_frame.winfo_children():
                widget.destroy()

            # 在后台线程中处理
            def process_segmentation():
                try:
                    # 获取字符分割结果
                    char_dict = self.font_creator.extract_characters_from_image(
                        image_path, text_content, auto_segment=True
                    )

                    # 在主线程中更新UI
                    self.root.after(0, lambda: self.display_segmentation_result(char_dict))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("字符分割失败", str(e)))

            threading.Thread(target=process_segmentation, daemon=True).start()

        except Exception as e:
            self.handle_error("预览分割失败", str(e))

    def display_segmentation_result(self, char_dict):
        """显示分割结果"""
        try:
            self.update_status(f"分割完成，共识别到 {len(char_dict)} 个字符")

            # 创建结果显示区域
            result_frame = ttk.Frame(self.preview_frame)
            result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题
            title_label = ttk.Label(result_frame, text="字符分割结果",
                                   font=('Segoe UI', 12, 'bold'))
            title_label.pack(pady=(0, 10))

            # 字符网格显示
            chars_frame = ttk.Frame(result_frame)
            chars_frame.pack(fill=tk.BOTH, expand=True)

            row = 0
            col = 0
            max_cols = 8  # 每行最多显示8个字符

            for char, char_images in char_dict.items():
                if char_images:  # 确保有图像数据
                    char_img = char_images[0]  # 取第一个图像

                    # 创建字符容器
                    char_container = ttk.Frame(chars_frame, relief=tk.RIDGE, borderwidth=1)
                    char_container.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

                    # 显示字符图像
                    try:
                        # 调整图像大小用于显示
                        display_img = cv2.resize(char_img, (60, 60))
                        # 转换为PIL图像
                        pil_img = Image.fromarray(display_img)
                        photo = ImageTk.PhotoImage(pil_img)

                        img_label = ttk.Label(char_container, image=photo)
                        img_label.image = photo  # 保持引用
                        img_label.pack(pady=5)

                    except Exception as e:
                        # 如果图像显示失败，显示占位符
                        placeholder_label = ttk.Label(char_container, text="[图像]",
                                                     width=8, height=4, relief=tk.SUNKEN)
                        placeholder_label.pack(pady=5)

                    # 显示字符文本
                    char_label = ttk.Label(char_container, text=f"'{char}'",
                                          font=('Segoe UI', 10, 'bold'))
                    char_label.pack()

                    col += 1
                    if col >= max_cols:
                        col = 0
                        row += 1

            # 配置网格权重
            for i in range(max_cols):
                chars_frame.grid_columnconfigure(i, weight=1)

        except Exception as e:
            self.handle_error("显示分割结果失败", str(e))

    def create_font_library(self):
        """制作字库"""
        image_path = self.image_path_var.get()
        text_content = self.text_content_var.get().strip()

        if not image_path:
            messagebox.showwarning("警告", "请先选择图像文件")
            return

        if not text_content:
            messagebox.showwarning("警告", "请输入图像中的文字内容")
            return

        try:
            self.update_status("正在制作字库...", True)

            def process_creation():
                try:
                    # 获取字库信息
                    font_info = {
                        "name": self.library_name_var.get(),
                        "description": self.library_desc_var.get(),
                        "source_image": os.path.basename(image_path)
                    }

                    # 创建字库
                    library_data = self.font_creator.create_from_image(
                        image_path, text_content, font_info, auto_segment=True
                    )

                    # 更新元数据
                    self.font_creator.library_data["metadata"]["description"] = font_info["description"]

                    self.root.after(0, lambda: self.on_library_created(library_data))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("制作字库失败", str(e)))

            threading.Thread(target=process_creation, daemon=True).start()

        except Exception as e:
            self.handle_error("制作字库失败", str(e))

    def on_library_created(self, library_data):
        """字库创建完成回调"""
        char_count = library_data.get("metadata", {}).get("total_chars", 0)
        self.update_status(f"字库制作完成，包含 {char_count} 个字符")
        messagebox.showinfo("成功", f"字库制作完成！\n包含 {char_count} 个字符")

    def save_font_library(self):
        """保存字库"""
        if not self.font_creator.library_data.get("characters"):
            messagebox.showwarning("警告", "请先制作字库")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存字库文件",
            defaultextension=".json",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                library_name = self.library_name_var.get() or "字库"
                if self.font_creator.save_library(file_path, library_name):
                    self.update_status(f"字库已保存: {os.path.basename(file_path)}")
                    messagebox.showinfo("成功", f"字库已保存到:\n{file_path}")
                else:
                    messagebox.showerror("错误", "保存字库失败")
            except Exception as e:
                self.handle_error("保存字库失败", str(e))

    # ==================== 管理字库模块 ====================

    def show_manage_module(self):
        """显示管理字库模块"""
        # 创建主容器
        manage_frame = ttk.Frame(self.content_frame)
        manage_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧控制面板
        left_frame = ttk.LabelFrame(manage_frame, text="字库管理操作", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.config(width=350)
        left_frame.pack_propagate(False)

        # 字库文件操作
        file_ops_frame = ttk.Frame(left_frame)
        file_ops_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(file_ops_frame, text="字库文件操作:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # 加载字库按钮
        ttk.Button(file_ops_frame, text="📂 加载字库文件", style="Primary.TButton",
                  command=self.load_library_file).pack(fill=tk.X, pady=(5, 5))

        # 保存字库按钮
        ttk.Button(file_ops_frame, text="💾 保存选中字库", style="Success.TButton",
                  command=self.save_selected_library).pack(fill=tk.X, pady=(0, 5))

        # 字库管理操作
        manage_ops_frame = ttk.Frame(left_frame)
        manage_ops_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(manage_ops_frame, text="字库管理操作:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # 合并字库按钮
        ttk.Button(manage_ops_frame, text="🔗 合并选中字库", style="Warning.TButton",
                  command=self.merge_selected_libraries).pack(fill=tk.X, pady=(5, 5))

        # 优化字库按钮
        ttk.Button(manage_ops_frame, text="⚡ 优化选中字库", style="Plain.TButton",
                  command=self.optimize_selected_library).pack(fill=tk.X, pady=(0, 5))

        # 删除字库按钮
        ttk.Button(manage_ops_frame, text="🗑️ 删除选中字库", style="Danger.TButton",
                  command=self.delete_selected_library).pack(fill=tk.X, pady=(0, 5))

        # 字库信息显示
        info_frame = ttk.LabelFrame(left_frame, text="字库信息", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建滚动文本框显示字库信息
        self.library_info_text = scrolledtext.ScrolledText(info_frame, height=8, width=30,
                                                          font=('Consolas', 9))
        self.library_info_text.pack(fill=tk.BOTH, expand=True)

        # 右侧字库列表
        right_frame = ttk.LabelFrame(manage_frame, text="已加载字库列表", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建字库列表
        self.create_library_list(right_frame)

        # 初始显示提示
        self.update_library_info("请加载字库文件以查看详细信息")

    def create_library_list(self, parent):
        """创建字库列表"""
        # 创建Treeview显示字库列表
        columns = ("name", "chars", "templates", "description")
        self.library_tree = ttk.Treeview(parent, columns=columns, show="tree headings", height=15)

        # 设置列标题
        self.library_tree.heading("#0", text="字库名称")
        self.library_tree.heading("name", text="显示名称")
        self.library_tree.heading("chars", text="字符数")
        self.library_tree.heading("templates", text="模板数")
        self.library_tree.heading("description", text="描述")

        # 设置列宽
        self.library_tree.column("#0", width=120)
        self.library_tree.column("name", width=120)
        self.library_tree.column("chars", width=80)
        self.library_tree.column("templates", width=80)
        self.library_tree.column("description", width=200)

        # 添加滚动条
        tree_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.library_tree.yview)
        self.library_tree.configure(yscrollcommand=tree_scroll.set)

        # 布局
        self.library_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定选择事件
        self.library_tree.bind("<<TreeviewSelect>>", self.on_library_select)

    def load_library_file(self):
        """加载字库文件"""
        file_path = filedialog.askopenfilename(
            title="选择字库文件",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                library_name = os.path.splitext(os.path.basename(file_path))[0]

                # 检查是否已加载
                if library_name in self.loaded_libraries:
                    if not messagebox.askyesno("确认", f"字库 '{library_name}' 已加载，是否重新加载？"):
                        return

                self.update_status("正在加载字库...", True)

                # 使用字库管理器加载
                if self.font_manager.load_library(file_path, library_name):
                    # 保存到本地记录
                    self.loaded_libraries[library_name] = {
                        "path": file_path,
                        "data": self.font_manager.libraries[library_name]
                    }

                    # 更新列表显示
                    self.refresh_library_list()
                    self.update_status(f"字库 '{library_name}' 加载成功")
                    messagebox.showinfo("成功", f"字库 '{library_name}' 加载成功")
                else:
                    self.handle_error("加载失败", "字库文件格式错误或文件损坏")

            except Exception as e:
                self.handle_error("加载字库失败", str(e))

    def refresh_library_list(self):
        """刷新字库列表显示"""
        # 清空现有项目
        for item in self.library_tree.get_children():
            self.library_tree.delete(item)

        # 添加已加载的字库
        for lib_name, lib_info in self.loaded_libraries.items():
            lib_data = lib_info["data"]
            metadata = lib_data.get("metadata", {})

            char_count = metadata.get("total_chars", 0)
            template_count = sum(len(char_data.get("templates", []))
                               for char_data in lib_data.get("characters", {}).values())
            description = metadata.get("description", "")

            self.library_tree.insert("", tk.END, iid=lib_name, text=lib_name,
                                    values=(lib_name, char_count, template_count, description))

    def on_library_select(self, event):
        """字库选择事件处理"""
        selection = self.library_tree.selection()
        if selection:
            lib_name = selection[0]
            if lib_name in self.loaded_libraries:
                self.show_library_details(lib_name)

    def show_library_details(self, lib_name):
        """显示字库详细信息"""
        lib_info = self.loaded_libraries[lib_name]
        lib_data = lib_info["data"]
        metadata = lib_data.get("metadata", {})

        info_text = f"字库名称: {lib_name}\n"
        info_text += f"文件路径: {lib_info['path']}\n"
        info_text += f"版本: {lib_data.get('version', 'N/A')}\n"
        info_text += f"字符数量: {metadata.get('total_chars', 0)}\n"
        info_text += f"创建时间: {metadata.get('created_time', 'N/A')}\n"
        info_text += f"保存时间: {metadata.get('saved_time', 'N/A')}\n"
        info_text += f"描述: {metadata.get('description', '无')}\n\n"

        # 显示字符列表
        characters = list(lib_data.get("characters", {}).keys())
        if characters:
            info_text += f"包含字符 ({len(characters)} 个):\n"
            # 每行显示20个字符
            for i in range(0, len(characters), 20):
                line_chars = characters[i:i+20]
                info_text += "".join(line_chars) + "\n"

        self.update_library_info(info_text)

    def update_library_info(self, info_text):
        """更新字库信息显示"""
        self.library_info_text.delete(1.0, tk.END)
        self.library_info_text.insert(1.0, info_text)

    def save_selected_library(self):
        """保存选中的字库"""
        selection = self.library_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要保存的字库")
            return

        lib_name = selection[0]
        if lib_name not in self.loaded_libraries:
            messagebox.showerror("错误", "选中的字库不存在")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存字库文件",
            defaultextension=".json",
            initialvalue=f"{lib_name}.json",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                lib_data = self.loaded_libraries[lib_name]["data"]

                # 更新保存时间
                lib_data["metadata"]["saved_time"] = datetime.now().isoformat()

                with open(file_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(lib_data, f, ensure_ascii=False, indent=2)

                self.update_status(f"字库 '{lib_name}' 已保存")
                messagebox.showinfo("成功", f"字库已保存到:\n{file_path}")

            except Exception as e:
                self.handle_error("保存字库失败", str(e))

    def merge_selected_libraries(self):
        """合并选中的字库"""
        selection = self.library_tree.selection()
        if len(selection) < 2:
            messagebox.showwarning("警告", "请选择至少两个字库进行合并")
            return

        # 获取合并后的字库名称
        merge_name = tk.simpledialog.askstring("合并字库", "请输入合并后的字库名称:",
                                              initialvalue="merged_library")
        if not merge_name:
            return

        try:
            self.update_status("正在合并字库...", True)

            # 使用字库管理器合并
            selected_libs = [lib for lib in selection if lib in self.loaded_libraries]

            if self.font_manager.merge_libraries(selected_libs, merge_name):
                # 添加到本地记录
                self.loaded_libraries[merge_name] = {
                    "path": f"<合并字库: {', '.join(selected_libs)}>",
                    "data": self.font_manager.libraries[merge_name]
                }

                self.refresh_library_list()
                self.update_status(f"字库合并完成: {merge_name}")
                messagebox.showinfo("成功", f"已成功合并 {len(selected_libs)} 个字库")
            else:
                self.handle_error("合并失败", "字库合并过程中出现错误")

        except Exception as e:
            self.handle_error("合并字库失败", str(e))

    def optimize_selected_library(self):
        """优化选中的字库"""
        selection = self.library_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要优化的字库")
            return

        lib_name = selection[0]
        if lib_name not in self.loaded_libraries:
            messagebox.showerror("错误", "选中的字库不存在")
            return

        try:
            self.update_status("正在优化字库...", True)

            # 使用字库管理器优化
            if self.font_manager.optimize_library(lib_name):
                # 更新本地记录
                self.loaded_libraries[lib_name]["data"] = self.font_manager.libraries[lib_name]

                self.refresh_library_list()
                self.show_library_details(lib_name)  # 刷新详细信息
                self.update_status(f"字库 '{lib_name}' 优化完成")
                messagebox.showinfo("成功", f"字库 '{lib_name}' 优化完成")
            else:
                self.handle_error("优化失败", "字库优化过程中出现错误")

        except Exception as e:
            self.handle_error("优化字库失败", str(e))

    def delete_selected_library(self):
        """删除选中的字库"""
        selection = self.library_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的字库")
            return

        lib_name = selection[0]
        if lib_name not in self.loaded_libraries:
            messagebox.showerror("错误", "选中的字库不存在")
            return

        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除字库 '{lib_name}' 吗？\n此操作仅从内存中移除，不会删除文件。"):
            try:
                # 从本地记录中删除
                del self.loaded_libraries[lib_name]

                # 从字库管理器中删除
                if lib_name in self.font_manager.libraries:
                    del self.font_manager.libraries[lib_name]

                self.refresh_library_list()
                self.update_library_info("请选择字库以查看详细信息")
                self.update_status(f"字库 '{lib_name}' 已删除")

            except Exception as e:
                self.handle_error("删除字库失败", str(e))

    # ==================== 屏幕找字模块 ====================

    def show_search_module(self):
        """显示屏幕找字模块"""
        # 创建主容器
        search_frame = ttk.Frame(self.content_frame)
        search_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧控制面板
        left_frame = ttk.LabelFrame(search_frame, text="屏幕找字设置", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.config(width=350)
        left_frame.pack_propagate(False)

        # 字库选择
        library_frame = ttk.Frame(left_frame)
        library_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(library_frame, text="选择字库:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # 字库下拉选择框
        self.search_library_var = tk.StringVar()
        self.search_library_combo = ttk.Combobox(library_frame, textvariable=self.search_library_var,
                                                state="readonly", width=30)
        self.search_library_combo.pack(fill=tk.X, pady=(5, 0))

        # 刷新字库列表按钮
        ttk.Button(library_frame, text="🔄 刷新字库列表", style="Plain.TButton",
                  command=self.refresh_search_libraries).pack(fill=tk.X, pady=(5, 0))

        # 查找设置
        search_settings_frame = ttk.Frame(left_frame)
        search_settings_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(search_settings_frame, text="查找设置:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # 查找文字输入
        text_search_frame = ttk.Frame(search_settings_frame)
        text_search_frame.pack(fill=tk.X, pady=(5, 5))
        ttk.Label(text_search_frame, text="查找文字:").pack(side=tk.LEFT)
        self.search_text_var = tk.StringVar()
        self.search_text_entry = ttk.Entry(text_search_frame, textvariable=self.search_text_var, width=20)
        self.search_text_entry.pack(side=tk.RIGHT)

        # 相似度阈值
        threshold_frame = ttk.Frame(search_settings_frame)
        threshold_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(threshold_frame, text="相似度:").pack(side=tk.LEFT)
        self.threshold_var = tk.DoubleVar(value=0.8)
        threshold_scale = ttk.Scale(threshold_frame, from_=0.1, to=1.0, variable=self.threshold_var,
                                   orient=tk.HORIZONTAL, length=150)
        threshold_scale.pack(side=tk.RIGHT, padx=(5, 0))
        self.threshold_label = ttk.Label(threshold_frame, text="0.8")
        self.threshold_label.pack(side=tk.RIGHT)

        # 绑定阈值变化事件
        threshold_scale.configure(command=self.on_threshold_change)

        # 最大结果数
        max_results_frame = ttk.Frame(search_settings_frame)
        max_results_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(max_results_frame, text="最大结果:").pack(side=tk.LEFT)
        self.max_results_var = tk.IntVar(value=10)
        max_results_spin = ttk.Spinbox(max_results_frame, from_=1, to=100, width=10,
                                      textvariable=self.max_results_var)
        max_results_spin.pack(side=tk.RIGHT)

        # 搜索区域设置
        region_frame = ttk.Frame(search_settings_frame)
        region_frame.pack(fill=tk.X, pady=(0, 5))

        self.use_region_var = tk.BooleanVar()
        region_check = ttk.Checkbutton(region_frame, text="限制搜索区域",
                                      variable=self.use_region_var,
                                      command=self.toggle_region_settings)
        region_check.pack(anchor=tk.W)

        # 区域坐标输入（默认隐藏）
        self.region_coords_frame = ttk.Frame(search_settings_frame)

        coords_grid = ttk.Frame(self.region_coords_frame)
        coords_grid.pack(fill=tk.X, pady=(5, 0))

        # X, Y, Width, Height 输入
        ttk.Label(coords_grid, text="X:").grid(row=0, column=0, sticky=tk.W)
        self.region_x_var = tk.IntVar(value=0)
        ttk.Entry(coords_grid, textvariable=self.region_x_var, width=8).grid(row=0, column=1, padx=(2, 5))

        ttk.Label(coords_grid, text="Y:").grid(row=0, column=2, sticky=tk.W)
        self.region_y_var = tk.IntVar(value=0)
        ttk.Entry(coords_grid, textvariable=self.region_y_var, width=8).grid(row=0, column=3, padx=(2, 5))

        ttk.Label(coords_grid, text="W:").grid(row=1, column=0, sticky=tk.W)
        self.region_w_var = tk.IntVar(value=800)
        ttk.Entry(coords_grid, textvariable=self.region_w_var, width=8).grid(row=1, column=1, padx=(2, 5))

        ttk.Label(coords_grid, text="H:").grid(row=1, column=2, sticky=tk.W)
        self.region_h_var = tk.IntVar(value=600)
        ttk.Entry(coords_grid, textvariable=self.region_h_var, width=8).grid(row=1, column=3, padx=(2, 5))

        # 操作按钮
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(button_frame, text="🔍 开始查找", style="Primary.TButton",
                  command=self.start_text_search).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="📷 截图预览", style="Success.TButton",
                  command=self.preview_search_area).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="🛑 停止查找", style="Danger.TButton",
                  command=self.stop_text_search).pack(fill=tk.X)

        # 右侧结果显示区域
        right_frame = ttk.LabelFrame(search_frame, text="查找结果", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建结果显示区域
        self.create_search_results_area(right_frame)

        # 初始化
        self.refresh_search_libraries()
        self.search_running = False

    def create_search_results_area(self, parent):
        """创建搜索结果显示区域"""
        # 结果列表
        columns = ("text", "x", "y", "width", "height", "similarity")
        self.results_tree = ttk.Treeview(parent, columns=columns, show="tree headings", height=12)

        # 设置列标题
        self.results_tree.heading("#0", text="序号")
        self.results_tree.heading("text", text="文字")
        self.results_tree.heading("x", text="X")
        self.results_tree.heading("y", text="Y")
        self.results_tree.heading("width", text="宽度")
        self.results_tree.heading("height", text="高度")
        self.results_tree.heading("similarity", text="相似度")

        # 设置列宽
        self.results_tree.column("#0", width=50)
        self.results_tree.column("text", width=80)
        self.results_tree.column("x", width=60)
        self.results_tree.column("y", width=60)
        self.results_tree.column("width", width=60)
        self.results_tree.column("height", width=60)
        self.results_tree.column("similarity", width=80)

        # 添加滚动条
        results_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scroll.set)

        # 布局
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.results_tree.bind("<Double-1>", self.on_result_double_click)

        # 右键菜单
        self.create_results_context_menu()

    def create_results_context_menu(self):
        """创建结果列表右键菜单"""
        self.results_menu = tk.Menu(self.root, tearoff=0)
        self.results_menu.add_command(label="点击此位置", command=self.click_selected_result)
        self.results_menu.add_command(label="复制坐标", command=self.copy_selected_coordinates)
        self.results_menu.add_separator()
        self.results_menu.add_command(label="清空结果", command=self.clear_search_results)

        # 绑定右键菜单
        self.results_tree.bind("<Button-3>", self.show_results_context_menu)

    def refresh_search_libraries(self):
        """刷新搜索字库列表"""
        # 获取已加载的字库列表
        libraries = list(self.loaded_libraries.keys())

        # 更新下拉框
        self.search_library_combo['values'] = libraries

        # 如果有字库，默认选择第一个
        if libraries:
            if not self.search_library_var.get() or self.search_library_var.get() not in libraries:
                self.search_library_var.set(libraries[0])
        else:
            self.search_library_var.set("")

    def on_threshold_change(self, value):
        """阈值变化事件处理"""
        self.threshold_label.config(text=f"{float(value):.2f}")

    def toggle_region_settings(self):
        """切换区域设置显示"""
        if self.use_region_var.get():
            self.region_coords_frame.pack(fill=tk.X, pady=(0, 5))
        else:
            self.region_coords_frame.pack_forget()

    def start_text_search(self):
        """开始文字查找"""
        # 检查字库选择
        library_name = self.search_library_var.get()
        if not library_name:
            messagebox.showwarning("警告", "请先选择字库")
            return

        # 检查查找文字
        search_text = self.search_text_var.get().strip()
        if not search_text:
            messagebox.showwarning("警告", "请输入要查找的文字")
            return

        if self.search_running:
            messagebox.showinfo("提示", "搜索正在进行中...")
            return

        try:
            self.search_running = True
            self.update_status("正在搜索文字...", True)

            # 在后台线程中执行搜索
            def search_thread():
                try:
                    # 设置搜索区域
                    region = None
                    if self.use_region_var.get():
                        region = (
                            self.region_x_var.get(),
                            self.region_y_var.get(),
                            self.region_w_var.get(),
                            self.region_h_var.get()
                        )

                    # 加载字库到图色脚本
                    if library_name in self.loaded_libraries:
                        lib_path = self.loaded_libraries[library_name]["path"]
                        if not lib_path.startswith("<"):  # 不是合并字库
                            self.image_script.load_font_library(lib_path, library_name)

                    # 执行搜索
                    results = []
                    threshold = self.threshold_var.get()
                    max_results = self.max_results_var.get()

                    # 逐字符搜索
                    for char in search_text:
                        char_results = self.image_script.find_character(
                            char, region=region, threshold=threshold, max_results=max_results
                        )

                        for result in char_results:
                            x, y, w, h = result
                            results.append({
                                "text": char,
                                "x": x,
                                "y": y,
                                "width": w,
                                "height": h,
                                "similarity": threshold  # 实际应该计算真实相似度
                            })

                    # 在主线程中更新结果
                    self.root.after(0, lambda: self.display_search_results(results))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("搜索失败", str(e)))
                finally:
                    self.root.after(0, lambda: setattr(self, 'search_running', False))
                    self.root.after(0, lambda: self.update_status("搜索完成"))

            threading.Thread(target=search_thread, daemon=True).start()

        except Exception as e:
            self.search_running = False
            self.handle_error("启动搜索失败", str(e))

    def display_search_results(self, results):
        """显示搜索结果"""
        # 清空现有结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # 添加新结果
        for i, result in enumerate(results, 1):
            self.results_tree.insert("", tk.END, iid=str(i), text=str(i),
                                    values=(
                                        result["text"],
                                        result["x"],
                                        result["y"],
                                        result["width"],
                                        result["height"],
                                        f"{result['similarity']:.2f}"
                                    ))

        self.update_status(f"找到 {len(results)} 个匹配结果")

    def preview_search_area(self):
        """预览搜索区域"""
        try:
            # 获取搜索区域
            region = None
            if self.use_region_var.get():
                region = (
                    self.region_x_var.get(),
                    self.region_y_var.get(),
                    self.region_w_var.get(),
                    self.region_h_var.get()
                )

            # 截图
            screenshot_path = "search_preview.png"
            self.image_script.save_screenshot(screenshot_path, region=region)

            # 显示截图预览窗口
            self.show_screenshot_preview(screenshot_path)

        except Exception as e:
            self.handle_error("截图预览失败", str(e))

    def show_screenshot_preview(self, image_path):
        """显示截图预览窗口"""
        try:
            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("搜索区域预览")
            preview_window.geometry("600x500")

            # 加载并显示图像
            from PIL import Image, ImageTk
            img = Image.open(image_path)

            # 调整图像大小以适应窗口
            img.thumbnail((580, 450), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(img)

            # 显示图像
            img_label = ttk.Label(preview_window, image=photo)
            img_label.image = photo  # 保持引用
            img_label.pack(pady=10)

            # 关闭按钮
            ttk.Button(preview_window, text="关闭",
                      command=preview_window.destroy).pack(pady=10)

        except Exception as e:
            self.handle_error("显示预览失败", str(e))

    def stop_text_search(self):
        """停止文字查找"""
        if self.search_running:
            self.search_running = False
            self.update_status("搜索已停止")
        else:
            messagebox.showinfo("提示", "当前没有正在进行的搜索")

    def on_result_double_click(self, event):
        """结果双击事件处理"""
        self.click_selected_result()

    def show_results_context_menu(self, event):
        """显示结果右键菜单"""
        # 选择点击的项目
        item = self.results_tree.identify_row(event.y)
        if item:
            self.results_tree.selection_set(item)
            self.results_menu.post(event.x_root, event.y_root)

    def click_selected_result(self):
        """点击选中的结果位置"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个结果")
            return

        try:
            item = selection[0]
            values = self.results_tree.item(item, "values")

            x = int(values[1])
            y = int(values[2])
            width = int(values[3])
            height = int(values[4])

            # 计算中心点
            center_x = x + width // 2
            center_y = y + height // 2

            # 执行点击
            self.image_script.click(center_x, center_y)
            self.update_status(f"已点击位置: ({center_x}, {center_y})")

        except Exception as e:
            self.handle_error("点击失败", str(e))

    def copy_selected_coordinates(self):
        """复制选中结果的坐标"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个结果")
            return

        try:
            item = selection[0]
            values = self.results_tree.item(item, "values")

            x = values[1]
            y = values[2]
            width = values[3]
            height = values[4]

            # 复制到剪贴板
            coord_text = f"({x}, {y}, {width}, {height})"
            self.root.clipboard_clear()
            self.root.clipboard_append(coord_text)

            self.update_status(f"坐标已复制: {coord_text}")

        except Exception as e:
            self.handle_error("复制坐标失败", str(e))

    def clear_search_results(self):
        """清空搜索结果"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.update_status("搜索结果已清空")

    def run(self):
        """运行UI界面"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()
        except Exception as e:
            print(f"UI运行错误: {e}")
            messagebox.showerror("错误", f"UI运行错误: {e}")


def main():
    """主函数"""
    if not UI_AVAILABLE:
        print("UI依赖库未安装，无法启动图形界面")
        print("请安装: pip install pillow")
        return
    
    try:
        app = FontLibraryUI()
        app.run()
    except Exception as e:
        print(f"启动UI失败: {e}")


if __name__ == "__main__":
    main()

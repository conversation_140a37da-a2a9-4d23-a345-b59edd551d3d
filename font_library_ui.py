#!/usr/bin/env python3
"""
字库制作UI界面
为zhaoTuZhaoSe.py提供图形化界面
"""

try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    from PIL import Image, ImageTk
    import threading
    UI_AVAILABLE = True
except ImportError as e:
    print(f"UI依赖库未安装: {e}")
    print("请安装: pip install pillow")
    UI_AVAILABLE = False

import os
import sys
import cv2
import numpy as np
from datetime import datetime

# 导入主功能模块
from zhaoTuZhaoSe import FontLibraryCreator, FontLibraryManager, ImageColorScript

class FontLibraryUI:
    """
    字库制作UI界面类
    提供图形化界面来制作、管理字库和屏幕找字功能
    """
    
    def __init__(self):
        """初始化UI界面"""
        if not UI_AVAILABLE:
            raise ImportError("UI依赖库未安装，请安装: pip install pillow")
            
        self.root = tk.Tk()
        self.root.title("字库制作工具 - zhaoTuZhaoSe")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 初始化功能类实例
        self.font_creator = FontLibraryCreator()
        self.font_manager = FontLibraryManager()
        self.image_script = ImageColorScript()
        
        # 当前选中的功能模块
        self.current_module = "create"
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_main_interface()
        
        # 状态变量
        self.loaded_libraries = {}  # 已加载的字库
        
    def setup_styles(self):
        """设置UI样式"""
        self.style = ttk.Style()
        
        # 使用现代主题
        available_themes = self.style.theme_names()
        if 'vista' in available_themes:
            self.style.theme_use('vista')
        elif 'winnative' in available_themes:
            self.style.theme_use('winnative')
        else:
            self.style.theme_use('clam')
        
        # 定义现代化按钮样式 - 参考button_style_demo.py
        # Plain button style (white background, gray text and border)
        self.style.configure("Plain.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#6B7280',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Plain.TButton",
                     background=[('active', '#F9FAFB'),
                               ('pressed', '#F3F4F6'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#6B7280'),
                                ('!focus', '#6B7280'),
                                ('disabled', '#E5E7EB')])

        # Primary button style (white background, blue text and border)
        self.style.configure("Primary.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#3B82F6',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Primary.TButton",
                     background=[('active', '#EBF4FF'),
                               ('pressed', '#DBEAFE'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#3B82F6'),
                                ('!focus', '#3B82F6'),
                                ('disabled', '#E5E7EB')])

        # Success button style (white background, green text and border)
        self.style.configure("Success.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#10B981',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Success.TButton",
                     background=[('active', '#ECFDF5'),
                               ('pressed', '#D1FAE5'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#10B981'),
                                ('!focus', '#10B981'),
                                ('disabled', '#E5E7EB')])

        # Warning button style (white background, orange text and border)
        self.style.configure("Warning.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#F59E0B',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Warning.TButton",
                     background=[('active', '#FFFBEB'),
                               ('pressed', '#FEF3C7'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#F59E0B'),
                                ('!focus', '#F59E0B'),
                                ('disabled', '#E5E7EB')])

        # Danger button style (white background, red text and border)
        self.style.configure("Danger.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#EF4444',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Danger.TButton",
                     background=[('active', '#FEF2F2'),
                               ('pressed', '#FECACA'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#EF4444'),
                                ('!focus', '#EF4444'),
                                ('disabled', '#E5E7EB')])
    
    def create_main_interface(self):
        """创建主界面"""
        # 创建主容器
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        title_label = ttk.Label(main_frame, text="🔤 字库制作工具", 
                               font=('Segoe UI', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 创建导航栏
        self.create_navigation(main_frame)
        
        # 创建主内容区域
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # 创建状态栏
        self.create_status_bar(main_frame)
        
        # 默认显示制作字库界面
        self.show_create_module()
    
    def create_navigation(self, parent):
        """创建导航栏"""
        nav_frame = ttk.Frame(parent)
        nav_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 导航按钮
        nav_buttons = [
            ("📝 制作字库", "create", "Primary.TButton"),
            ("📚 管理字库", "manage", "Success.TButton"),
            ("🔍 屏幕找字", "search", "Warning.TButton")
        ]
        
        for text, module, style in nav_buttons:
            btn = ttk.Button(nav_frame, text=text, style=style,
                           command=lambda m=module: self.switch_module(m))
            btn.pack(side=tk.LEFT, padx=(0, 10))
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, text="就绪", 
                                     font=('Segoe UI', 9))
        self.status_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
    
    def switch_module(self, module):
        """切换功能模块"""
        if self.current_module == module:
            return
            
        self.current_module = module
        
        # 清空当前内容
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # 显示对应模块
        if module == "create":
            self.show_create_module()
        elif module == "manage":
            self.show_manage_module()
        elif module == "search":
            self.show_search_module()
    
    def update_status(self, message, show_progress=False):
        """更新状态栏"""
        self.status_label.config(text=message)
        if show_progress:
            self.progress_bar.start()
        else:
            self.progress_bar.stop()
        self.root.update_idletasks()
    
    def handle_error(self, title, message):
        """处理错误"""
        self.update_status("操作失败")
        messagebox.showerror(title, message)
        print(f"错误: {title} - {message}")  # 同时输出到控制台用于调试

    # ==================== 制作字库模块 ====================

    def show_create_module(self):
        """显示制作字库模块"""
        # 创建主容器
        create_frame = ttk.Frame(self.content_frame)
        create_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧控制面板
        left_frame = ttk.LabelFrame(create_frame, text="字库制作设置", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.config(width=350)
        left_frame.pack_propagate(False)

        # 图像文件选择
        file_frame = ttk.Frame(left_frame)
        file_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(file_frame, text="选择图像文件:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, pady=(5, 0))

        self.image_path_var = tk.StringVar()
        self.image_path_entry = ttk.Entry(file_select_frame, textvariable=self.image_path_var,
                                         state='readonly')
        self.image_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(file_select_frame, text="浏览", style="Plain.TButton",
                  command=self.select_image_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 文字内容输入
        text_frame = ttk.Frame(left_frame)
        text_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(text_frame, text="图像中的文字内容:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.text_content_var = tk.StringVar()
        self.text_content_entry = ttk.Entry(text_frame, textvariable=self.text_content_var)
        self.text_content_entry.pack(fill=tk.X, pady=(5, 0))

        # 字库信息设置
        info_frame = ttk.Frame(left_frame)
        info_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(info_frame, text="字库信息:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # 字库名称
        name_frame = ttk.Frame(info_frame)
        name_frame.pack(fill=tk.X, pady=(5, 5))
        ttk.Label(name_frame, text="名称:").pack(side=tk.LEFT)
        self.library_name_var = tk.StringVar(value="新字库")
        ttk.Entry(name_frame, textvariable=self.library_name_var, width=20).pack(side=tk.RIGHT)

        # 字库描述
        desc_frame = ttk.Frame(info_frame)
        desc_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(desc_frame, text="描述:").pack(side=tk.LEFT)
        self.library_desc_var = tk.StringVar()
        ttk.Entry(desc_frame, textvariable=self.library_desc_var, width=20).pack(side=tk.RIGHT)

        # 操作按钮
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(button_frame, text="预览分割", style="Primary.TButton",
                  command=self.preview_segmentation).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="制作字库", style="Success.TButton",
                  command=self.create_font_library).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="保存字库", style="Warning.TButton",
                  command=self.save_font_library).pack(fill=tk.X)

        # 右侧预览区域
        right_frame = ttk.LabelFrame(create_frame, text="预览区域", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建滚动区域
        canvas = tk.Canvas(right_frame, bg='white')
        scrollbar_v = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=canvas.yview)
        scrollbar_h = ttk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=canvas.xview)

        self.preview_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        canvas.create_window((0, 0), window=self.preview_frame, anchor=tk.NW)

        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 绑定滚动事件
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        self.preview_frame.bind("<Configure>", configure_scroll_region)

        # 初始显示提示
        self.show_preview_hint()

    def show_preview_hint(self):
        """显示预览提示"""
        hint_label = ttk.Label(self.preview_frame,
                              text="请选择图像文件并输入文字内容，然后点击'预览分割'查看字符分割效果",
                              font=('Segoe UI', 11),
                              foreground='#6B7280')
        hint_label.pack(expand=True, pady=50)

    def select_image_file(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[
                ("图像文件", "*.png *.jpg *.jpeg *.bmp *.tiff"),
                ("PNG文件", "*.png"),
                ("JPEG文件", "*.jpg *.jpeg"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.image_path_var.set(file_path)
            self.update_status(f"已选择图像: {os.path.basename(file_path)}")

    def preview_segmentation(self):
        """预览字符分割"""
        image_path = self.image_path_var.get()
        text_content = self.text_content_var.get().strip()

        if not image_path:
            messagebox.showwarning("警告", "请先选择图像文件")
            return

        if not text_content:
            messagebox.showwarning("警告", "请输入图像中的文字内容")
            return

        try:
            self.update_status("正在分割字符...", True)

            # 清空预览区域
            for widget in self.preview_frame.winfo_children():
                widget.destroy()

            # 在后台线程中处理
            def process_segmentation():
                try:
                    # 获取字符分割结果
                    char_dict = self.font_creator.extract_characters_from_image(
                        image_path, text_content, auto_segment=True
                    )

                    # 在主线程中更新UI
                    self.root.after(0, lambda: self.display_segmentation_result(char_dict))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("字符分割失败", str(e)))

            threading.Thread(target=process_segmentation, daemon=True).start()

        except Exception as e:
            self.handle_error("预览分割失败", str(e))

    def display_segmentation_result(self, char_dict):
        """显示分割结果"""
        try:
            self.update_status(f"分割完成，共识别到 {len(char_dict)} 个字符")

            # 创建结果显示区域
            result_frame = ttk.Frame(self.preview_frame)
            result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题
            title_label = ttk.Label(result_frame, text="字符分割结果",
                                   font=('Segoe UI', 12, 'bold'))
            title_label.pack(pady=(0, 10))

            # 字符网格显示
            chars_frame = ttk.Frame(result_frame)
            chars_frame.pack(fill=tk.BOTH, expand=True)

            row = 0
            col = 0
            max_cols = 8  # 每行最多显示8个字符

            for char, char_images in char_dict.items():
                if char_images:  # 确保有图像数据
                    char_img = char_images[0]  # 取第一个图像

                    # 创建字符容器
                    char_container = ttk.Frame(chars_frame, relief=tk.RIDGE, borderwidth=1)
                    char_container.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

                    # 显示字符图像
                    try:
                        # 调整图像大小用于显示
                        display_img = cv2.resize(char_img, (60, 60))
                        # 转换为PIL图像
                        pil_img = Image.fromarray(display_img)
                        photo = ImageTk.PhotoImage(pil_img)

                        img_label = ttk.Label(char_container, image=photo)
                        img_label.image = photo  # 保持引用
                        img_label.pack(pady=5)

                    except Exception as e:
                        # 如果图像显示失败，显示占位符
                        placeholder_label = ttk.Label(char_container, text="[图像]",
                                                     width=8, height=4, relief=tk.SUNKEN)
                        placeholder_label.pack(pady=5)

                    # 显示字符文本
                    char_label = ttk.Label(char_container, text=f"'{char}'",
                                          font=('Segoe UI', 10, 'bold'))
                    char_label.pack()

                    col += 1
                    if col >= max_cols:
                        col = 0
                        row += 1

            # 配置网格权重
            for i in range(max_cols):
                chars_frame.grid_columnconfigure(i, weight=1)

        except Exception as e:
            self.handle_error("显示分割结果失败", str(e))

    def create_font_library(self):
        """制作字库"""
        image_path = self.image_path_var.get()
        text_content = self.text_content_var.get().strip()

        if not image_path:
            messagebox.showwarning("警告", "请先选择图像文件")
            return

        if not text_content:
            messagebox.showwarning("警告", "请输入图像中的文字内容")
            return

        try:
            self.update_status("正在制作字库...", True)

            def process_creation():
                try:
                    # 获取字库信息
                    font_info = {
                        "name": self.library_name_var.get(),
                        "description": self.library_desc_var.get(),
                        "source_image": os.path.basename(image_path)
                    }

                    # 创建字库
                    library_data = self.font_creator.create_from_image(
                        image_path, text_content, font_info, auto_segment=True
                    )

                    # 更新元数据
                    self.font_creator.library_data["metadata"]["description"] = font_info["description"]

                    self.root.after(0, lambda: self.on_library_created(library_data))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("制作字库失败", str(e)))

            threading.Thread(target=process_creation, daemon=True).start()

        except Exception as e:
            self.handle_error("制作字库失败", str(e))

    def on_library_created(self, library_data):
        """字库创建完成回调"""
        char_count = library_data.get("metadata", {}).get("total_chars", 0)
        self.update_status(f"字库制作完成，包含 {char_count} 个字符")
        messagebox.showinfo("成功", f"字库制作完成！\n包含 {char_count} 个字符")

    def save_font_library(self):
        """保存字库"""
        if not self.font_creator.library_data.get("characters"):
            messagebox.showwarning("警告", "请先制作字库")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存字库文件",
            defaultextension=".json",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                library_name = self.library_name_var.get() or "字库"
                if self.font_creator.save_library(file_path, library_name):
                    self.update_status(f"字库已保存: {os.path.basename(file_path)}")
                    messagebox.showinfo("成功", f"字库已保存到:\n{file_path}")
                else:
                    messagebox.showerror("错误", "保存字库失败")
            except Exception as e:
                self.handle_error("保存字库失败", str(e))

    # ==================== 管理字库模块 ====================

    def show_manage_module(self):
        """显示管理字库模块"""
        hint_label = ttk.Label(self.content_frame,
                              text="管理字库功能正在开发中...",
                              font=('Segoe UI', 14),
                              foreground='#6B7280')
        hint_label.pack(expand=True)

    # ==================== 屏幕找字模块 ====================

    def show_search_module(self):
        """显示屏幕找字模块"""
        hint_label = ttk.Label(self.content_frame,
                              text="屏幕找字功能正在开发中...",
                              font=('Segoe UI', 14),
                              foreground='#6B7280')
        hint_label.pack(expand=True)

    def run(self):
        """运行UI界面"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()
        except Exception as e:
            print(f"UI运行错误: {e}")
            messagebox.showerror("错误", f"UI运行错误: {e}")


def main():
    """主函数"""
    if not UI_AVAILABLE:
        print("UI依赖库未安装，无法启动图形界面")
        print("请安装: pip install pillow")
        return
    
    try:
        app = FontLibraryUI()
        app.run()
    except Exception as e:
        print(f"启动UI失败: {e}")


if __name__ == "__main__":
    main()

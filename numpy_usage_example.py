#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
numpy数组输入使用示例
展示您想要的使用方式
"""

from image_color_script import ImageColorScript

def main():
    """您想要的使用方式"""
    print("=== 您的使用方式示例 ===")
    print("功能: 截图 → 直接传入numpy数组搜索")
    print()
    
    # 创建脚本实例
    script = ImageColorScript()
    
    try:
        # 截取屏幕（返回numpy数组）
        print("📸 截取屏幕...")
        screen_img = script.capture_screen()
        print(f"   截图尺寸: {screen_img.shape}")
        print(f"   数据类型: {type(screen_img)}")
        
        # 直接传入numpy数组进行搜索
        print(f"\n🔍 智能搜索...")
        result = script.smart_find_image(
            screen_img,           # 直接传入截图数组
            "template.bmp",       # 模板文件路径
            mode='best',          # 'best' 或 'all'
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        print("result:", result)
        print("=" * 50)
        
        # 处理搜索结果
        if result:
            x, y, similarity = result
            print(f"✅ 找到匹配!")
            print(f"   位置: ({x}, {y})")
            print(f"   MSE相似度: {similarity:.3f}")
            print(f"   精度评级: {'完美' if similarity >= 0.999 else '优秀' if similarity >= 0.95 else '良好' if similarity >= 0.9 else '一般'}")
            
            # 可以直接点击找到的位置
            # script.click(x + 25, y + 25)  # 点击中心位置
            print(f"   💡 可以点击位置: ({x + 25}, {y + 25})")
        else:
            print("❌ 未找到匹配")
            print("   建议:")
            print("   • 检查模板文件是否存在")
            print("   • 降低阈值重试")
            print("   • 确认模板图像质量")
    
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
        print("   请确保 template.bmp 文件存在")
    except Exception as e:
        print(f"❌ 搜索过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def advanced_usage_examples():
    """高级使用示例"""
    print("\n=== 高级使用示例 ===")
    
    script = ImageColorScript()
    
    # 示例1: 实时监控
    print("📝 示例1: 实时监控模式")
    print("""
    import time
    
    script = ImageColorScript()
    
    while True:
        # 实时截图
        screen = script.capture_screen()
        
        # 搜索目标
        target = script.smart_find_image(
            screen, "target.png",
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        if target:
            x, y, similarity = target
            print(f'发现目标: ({x}, {y}), 精度: {similarity:.3f}')
            script.click(x + 25, y + 25)
            break
        
        time.sleep(0.1)  # 100ms间隔
    """)
    
    # 示例2: 批量搜索
    print("\n📝 示例2: 批量搜索模式")
    print("""
    script = ImageColorScript()
    
    # 一次截图，搜索多个目标
    screen = script.capture_screen()
    
    targets = [
        ("enemy1.png", "敌人1"),
        ("enemy2.png", "敌人2"),
        ("treasure.png", "宝箱")
    ]
    
    for template_file, name in targets:
        result = script.smart_find_image(
            screen, template_file,
            template_threshold=0.7,
            mse_threshold=0.9
        )
        
        if result:
            x, y, similarity = result
            print(f'找到{name}: ({x}, {y}), 精度: {similarity:.3f}')
    """)
    
    # 示例3: 高精度搜索
    print("\n📝 示例3: 高精度搜索模式")
    print("""
    script = ImageColorScript()
    
    screen = script.capture_screen()
    
    # 高精度搜索
    precise_result = script.smart_find_image(
        screen, "precise_target.png",
        template_threshold=0.6,   # 较低，找更多候选
        mse_threshold=0.95        # 较高，确保精度
    )
    
    if precise_result and precise_result[2] >= 0.95:
        x, y, similarity = precise_result
        print(f'高精度匹配: ({x}, {y}), 精度: {similarity:.3f}')
        # 执行精确操作
        script.click(x + 10, y + 10)
    """)

def performance_tips():
    """性能优化提示"""
    print("\n=== 性能优化提示 ===")
    
    tips = [
        "🚀 numpy数组输入的优势:",
        "",
        "1. 性能提升:",
        "   • 避免文件I/O操作",
        "   • 减少磁盘读写时间",
        "   • 内存操作更快",
        "",
        "2. 内存效率:",
        "   • 无需保存临时文件",
        "   • 减少磁盘空间占用",
        "   • 避免文件清理工作",
        "",
        "3. 代码简洁:",
        "   • 一行截图，一行搜索",
        "   • 无需管理临时文件",
        "   • 减少错误处理代码",
        "",
        "4. 实时应用:",
        "   • 适合游戏脚本",
        "   • 适合实时监控",
        "   • 适合高频操作",
        "",
        "5. 最佳实践:",
        "   • 复用截图进行多次搜索",
        "   • 预加载模板图像",
        "   • 合理设置搜索阈值"
    ]
    
    for tip in tips:
        print(f"  {tip}")

def troubleshooting():
    """故障排除"""
    print("\n=== 故障排除 ===")
    
    issues = [
        "🔧 常见问题解决:",
        "",
        "1. 模板文件未找到:",
        "   • 检查文件路径是否正确",
        "   • 确认文件扩展名",
        "   • 使用绝对路径",
        "",
        "2. 搜索结果为None:",
        "   • 降低template_threshold (如0.6)",
        "   • 降低mse_threshold (如0.85)",
        "   • 检查模板图像质量",
        "",
        "3. 性能问题:",
        "   • 使用合适的阈值",
        "   • 限制搜索区域",
        "   • 优化模板图像大小",
        "",
        "4. 精度问题:",
        "   • 提高mse_threshold",
        "   • 使用高质量模板",
        "   • 检查图像预处理",
        "",
        "5. 内存问题:",
        "   • 及时释放大图像",
        "   • 使用合适的图像尺寸",
        "   • 避免内存泄漏"
    ]
    
    for issue in issues:
        print(f"  {issue}")

if __name__ == "__main__":
    print("=" * 60)
    print("numpy数组输入使用示例")
    print("=" * 60)
    print("✅ 现在支持您想要的使用方式:")
    print("   screen = script.capture_screen()")
    print("   result = script.smart_find_image(screen, 'template.bmp')")
    print("=" * 60)
    
    main()
    advanced_usage_examples()
    performance_tips()
    troubleshooting()
    
    print("\n" + "=" * 60)
    print("🎉 功能已完美适配您的需求！")
    print("\n📋 支持的输入方式:")
    print("• 文件路径 + 文件路径")
    print("• numpy数组 + 文件路径")  
    print("• numpy数组 + numpy数组")
    print("• 文件路径 + numpy数组")
    print("\n🚀 推荐使用:")
    print("screen = script.capture_screen()")
    print("result = script.smart_find_image(screen, 'template.bmp')")

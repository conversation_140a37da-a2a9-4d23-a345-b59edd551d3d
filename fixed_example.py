# 修复后的图色脚本使用示例
# 使用新的 image_color_script.py 文件

from image_color_script import ImageColorScript

def find_imgPos():
    """测试图像位置查找功能"""
    # 创建脚本实例
    script = ImageColorScript()

    print("=== 图色脚本功能测试 ===")
    
    try:
        # 1. 查找小图在大图中的位置（最准确的方法）
        print("1. 在大图中查找小图...")
        position = script.find_image_in_image("test_full_screen.png", "test_region_screen.png")
        if position:
            print(f"   ✓ 找到位置: {position}")
            print(f"   这说明区域图确实是全屏图的一部分")
        else:
            print("   ! 未找到匹配位置（可能需要调整阈值）")

        # 2. 多种相似度比较方法
        print("\n2. 图像相似度比较...")
        
        # 直方图比较（推荐用于颜色分布比较）
        similarity_hist = script.compare_images("test_full_screen.png", "test_region_screen.png", method='histogram')
        print(f"   直方图相似度: {similarity_hist:.3f}")
        
        # MSE比较（像素级精确比较）
        similarity_mse = script.compare_images("test_full_screen.png", "test_region_screen.png", method='mse')
        print(f"   MSE相似度: {similarity_mse:.3f}")
        
        # SSIM比较（结构相似性）
        similarity_ssim = script.compare_images("test_full_screen.png", "test_region_screen.png", method='ssim')
        print(f"   SSIM相似度: {similarity_ssim:.3f}")

        # 3. 在屏幕中查找图像
        print("\n3. 在屏幕中查找图像...")
        results = script.find_image("test_region_screen.png", threshold=0.8)
        if results:
            print(f"   ✓ 在屏幕中找到图像: {results}")
            # 获取中心坐标
            center = script.find_image_center("test_region_screen.png", threshold=0.8)
            if center:
                print(f"   图像中心坐标: {center}")
        else:
            print("   ! 未在当前屏幕中找到图像")
            
        # 4. 测试截图功能
        print("\n4. 截图功能测试...")
        if script.save_screenshot("new_full_screenshot.png"):
            print("   ✓ 全屏截图保存成功: new_full_screenshot.png")
        
        if script.save_screenshot("new_region_screenshot.png", region=(0, 0, 300, 200)):
            print("   ✓ 区域截图保存成功: new_region_screenshot.png")
            
        # 5. 测试颜色相关功能
        print("\n5. 颜色功能测试...")
        center_x, center_y = script.screen_width // 2, script.screen_height // 2
        color = script.get_pixel_color(center_x, center_y)
        print(f"   屏幕中心点 ({center_x}, {center_y}) 颜色: {color}")
        
        # 查找白色像素
        white_positions = script.find_color((255, 255, 255), max_results=3, tolerance=20)
        print(f"   找到 {len(white_positions)} 个白色像素点")
        if white_positions:
            print(f"   前几个位置: {white_positions}")
        
        print("\n=== 测试完成 ===")
        print("✅ 所有功能正常工作！")
        
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        print("请确保 test_full_screen.png 和 test_region_screen.png 文件存在")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def demo_advanced_features():
    """演示高级功能"""
    print("\n=== 高级功能演示 ===")
    
    script = ImageColorScript()
    
    try:
        # 多点找色示例
        print("1. 多点找色演示...")
        color_pattern = [
            (0, 0, (255, 255, 255)),    # 基准点：白色
            (1, 0, (255, 255, 255)),    # 右边1像素：白色
            (0, 1, (255, 255, 255)),    # 下边1像素：白色
        ]
        
        result = script.find_multi_color(color_pattern, tolerance=30)
        if result:
            print(f"   ✓ 找到颜色模式，位置: {result}")
        else:
            print("   ! 未找到指定的颜色模式")
        
        # 颜色距离计算
        print("\n2. 颜色距离计算...")
        red = (0, 0, 255)      # BGR格式的红色
        green = (0, 255, 0)    # BGR格式的绿色
        near_red = (10, 10, 255)  # 接近红色
        
        distance1 = script.color_distance(red, green)
        distance2 = script.color_distance(red, near_red)
        
        print(f"   红色与绿色距离: {distance1:.2f}")
        print(f"   红色与近似红色距离: {distance2:.2f}")
        
        print("\n3. 鼠标键盘操作演示（安全模式）...")
        print("   注意：以下操作会实际控制鼠标键盘，请谨慎使用")
        
        # 获取当前鼠标位置（不移动）
        import pyautogui
        current_pos = pyautogui.position()
        print(f"   当前鼠标位置: {current_pos}")
        
        # 这里只演示方法调用，不实际执行
        print("   可用的鼠标键盘方法:")
        print("   - script.click(x, y)  # 点击")
        print("   - script.type_text('text')  # 输入文本")
        print("   - script.key_combination('ctrl', 'c')  # 组合键")
        
    except Exception as e:
        print(f"高级功能演示出现错误: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("修复后的图色脚本功能测试")
    print("=" * 60)
    print("使用文件: image_color_script.py")
    print("解决了原 zhaoTuZhaoSe.py 的空字节问题")
    print("=" * 60)
    
    find_imgPos()
    demo_advanced_features()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("\n📋 功能总结:")
    print("✅ 截图功能 - capture_screen(), save_screenshot()")
    print("✅ 找图功能 - find_image(), find_image_center()")
    print("✅ 找色功能 - find_color(), find_multi_color()")
    print("✅ 图像比较 - compare_images(), find_image_in_image()")
    print("✅ 鼠标键盘 - click(), type_text(), key_combination()")
    print("✅ 等待功能 - wait_for_image(), wait_for_color()")
    print("\n💡 使用提示:")
    print("- 新文件解决了空字节问题，可以正常导入")
    print("- 所有原有功能都已保留并改进")
    print("- 图像比较功能已修复，不再出现负值")

if __name__ == "__main__":
    main()

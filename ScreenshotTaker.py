import win32gui
import win32ui
import win32con
from PIL import Image


class ScreenshotTaker:
    def __init__(self):
        self.dcObj = None
        self.cDC = None
        self.bmp = None

    def take_screenshot(self, hwnd):
        # 获取窗口的设备上下文DC（Device Context）
        wDC = win32gui.GetWindowDC(hwnd)
        if not self.dcObj:
            self.dcObj = win32ui.CreateDCFromHandle(wDC)
            self.cDC = self.dcObj.CreateCompatibleDC()

        # 获取窗口尺寸
        rect = win32gui.GetWindowRect(hwnd)
        width = rect[2] - rect[0]
        height = rect[3] - rect[1]

        if not self.bmp or self.bmp.GetInfo()['bmWidth'] != width or self.bmp.GetInfo()['bmHeight'] != height:
            # 创建位图对象
            self.bmp = win32ui.CreateBitmap()
            self.bmp.CreateCompatibleBitmap(self.dcObj, width, height)

        self.cDC.SelectObject(self.bmp)

        # 从窗口的DC中拷贝图像到位图对象中
        self.cDC.BitBlt((0, 0), (width, height), self.dcObj, (0, 0), win32con.SRCCOPY)

        # 将位图转换为PIL可处理的格式
        bmpstr = self.bmp.GetBitmapBits(True)
        img = Image.frombuffer('RGB', (width, height), bmpstr, 'raw', 'BGRX', 0, 1)

        # 注意：不再在这里释放资源，而是在对象销毁时释放
        return img

    def __del__(self):
        """确保资源被释放"""
        if self.cDC:
            self.cDC.DeleteDC()
        if self.dcObj:
            self.dcObj.DeleteDC()
        if self.bmp:
            win32gui.DeleteObject(self.bmp.GetHandle())
        print("资源已释放")

    def cleanup(self):
        """显式释放资源"""
        if self.cDC:
            self.cDC.DeleteDC()
        if self.dcObj:
            self.dcObj.DeleteDC()
        if self.bmp:
            win32gui.DeleteObject(self.bmp.GetHandle())
        print("资源已显式释放")


# 使用示例
# if __name__ == "__main__":
#     screenshot_taker = ScreenshotTaker()
#     hwnd = win32gui.GetDesktopWindow()  # 示例：获取桌面窗口的句柄
#     img = screenshot_taker.take_screenshot(hwnd)
#     img.show()  # 显示截图
#
#     screenshot_taker.cleanup()

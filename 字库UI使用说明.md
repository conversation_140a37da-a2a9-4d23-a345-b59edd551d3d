# 字库制作工具UI使用说明

## 概述
字库制作工具UI（font_library_ui.py）是一个图形化界面工具，用于制作、管理字库和屏幕找字功能。

## 主要功能

### 1. 制作字库模块 📝
用于从图像中提取字符，创建字库文件。

**使用步骤：**
1. 点击"制作字库"标签
2. 选择包含文字的图像文件
3. 输入图像中的文字内容
4. 设置字库名称和描述
5. 点击"预览分割"查看字符分割效果
6. 点击"制作字库"创建字库
7. 点击"保存字库"将字库保存到文件

### 2. 管理字库模块 📚
用于加载、查看、合并、优化和管理字库文件。

**功能说明：**
- **加载字库文件**：从JSON文件加载字库
- **保存选中字库**：将字库保存到新文件
- **合并选中字库**：将多个字库合并为一个
- **优化选中字库**：去除重复模板，压缩字库
- **删除选中字库**：从内存中移除字库

**使用步骤：**
1. 点击"管理字库"标签
2. 点击"加载字库文件"选择字库JSON文件
3. 在右侧列表中查看已加载的字库
4. 选择字库查看详细信息
5. 使用各种管理操作按钮

### 3. 屏幕找字模块 🔍
用于在屏幕上查找指定的文字。

**使用步骤：**
1. 点击"屏幕找字"标签
2. 从下拉框选择要使用的字库
3. 点击"查看字库内容"确认字库包含要搜索的字符
4. 在"查找文字"框中输入要搜索的文字
5. 调整相似度阈值（0.1-1.0，推荐0.8）
6. 设置最大结果数量
7. 可选：勾选"限制搜索区域"并设置坐标
8. 点击"开始查找"执行搜索
9. 在结果列表中查看找到的位置
10. 双击结果或右键菜单进行操作

## 常见问题解决

### Q1: 屏幕找字时提示"字符不存在"
**原因：** 选择的字库中不包含要搜索的字符
**解决：** 
1. 点击"查看字库内容"确认字库包含的字符
2. 使用包含目标字符的字库
3. 或者制作包含目标字符的新字库

### Q2: 屏幕找字没有结果
**可能原因：**
1. 屏幕上没有该文字
2. 相似度阈值设置过高
3. 搜索区域设置不正确
4. 字体样式与字库不匹配

**解决方法：**
1. 确认屏幕上确实有要搜索的文字
2. 降低相似度阈值（如从0.8降到0.6）
3. 取消搜索区域限制或调整区域坐标
4. 使用相同字体样式的字库

### Q3: 字库制作时分割效果不好
**解决方法：**
1. 确保图像清晰，文字与背景对比度高
2. 输入的文字内容要与图像中的文字完全一致
3. 尝试预处理图像（调整亮度、对比度等）

## 技术说明

### 支持的文件格式
- **图像文件**：PNG, JPG, JPEG, BMP, TIFF
- **字库文件**：JSON格式

### 窗口居中
程序启动时会自动计算屏幕尺寸，将窗口居中显示。

### 多线程处理
耗时操作（如字符分割、字库制作、屏幕搜索）在后台线程执行，保持UI响应。

### 错误处理
所有操作都有完善的错误处理机制，出现问题时会显示详细的错误信息。

## 快捷操作

### 屏幕找字结果操作
- **双击结果**：自动点击对应位置
- **右键菜单**：
  - 点击此位置
  - 复制坐标
  - 清空结果

### 字库管理操作
- **选择多个字库**：按住Ctrl键点击多个字库进行合并
- **查看详细信息**：点击字库名称查看详细信息

## 注意事项

1. **字库文件路径**：建议使用英文路径，避免中文路径可能导致的问题
2. **图像质量**：制作字库时使用高质量、清晰的图像效果更好
3. **字符一致性**：输入的文字内容必须与图像中的文字完全一致
4. **相似度设置**：根据实际情况调整相似度阈值，过高可能找不到结果，过低可能误识别
5. **内存管理**：加载大量字库时注意内存使用情况

## 更新日志

### 最新版本修复
- ✅ 修复窗口居中显示问题
- ✅ 完善管理字库功能模块
- ✅ 完善屏幕找字功能模块
- ✅ 修复字库加载逻辑问题
- ✅ 改进错误提示和用户反馈
- ✅ 添加字库内容查看功能
- ✅ 修复文件保存对话框参数错误

---

如有问题或建议，请查看控制台输出的详细错误信息进行排查。

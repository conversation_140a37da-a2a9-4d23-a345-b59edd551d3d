#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字库功能使用示例
演示如何使用新增的字库制作和文字识别功能
"""

from zhaoTuZhaoSe import ImageColorScript, FontLibraryCreator, FontLibraryManager
import cv2
import numpy as np
import os

def example_create_font_library():
    """示例：创建字库"""
    print("=== 字库制作示例 ===")
    
    # 创建字库制作器
    creator = FontLibraryCreator()
    
    # 方法1：从单个图像创建字库
    # 假设你有一个包含文字 "Hello" 的图像文件
    try:
        # creator.create_from_image("text_image.png", "Hello")
        # creator.save_library("hello_font.json", "Hello字库")
        print("1. 从单个图像创建字库的方法：")
        print("   creator.create_from_image('text_image.png', 'Hello')")
        print("   creator.save_library('hello_font.json', 'Hello字库')")
    except Exception as e:
        print(f"   注意：需要准备实际的图像文件，错误：{e}")
    
    # 方法2：从多个图像创建字库
    print("\n2. 从多个图像创建字库的方法：")
    print("   image_text_pairs = [")
    print("       ('image1.png', '文字1'),")
    print("       ('image2.png', '文字2')")
    print("   ]")
    print("   creator.create_from_multiple_images(image_text_pairs)")
    
    # 方法3：预览字符分割
    print("\n3. 预览字符分割的方法：")
    print("   boxes = creator.preview_segmentation('text_image.png', 'preview.png')")
    
    return creator

def example_manage_font_library():
    """示例：管理字库"""
    print("\n=== 字库管理示例 ===")
    
    # 创建字库管理器
    manager = FontLibraryManager()
    
    # 加载字库
    print("1. 加载字库：")
    print("   manager.load_library('font1.json', 'font1')")
    print("   manager.load_library('font2.json', 'font2')")
    
    # 合并字库
    print("\n2. 合并字库：")
    print("   manager.merge_libraries(['font1', 'font2'], 'merged_font')")
    
    # 优化字库
    print("\n3. 优化字库（去重）：")
    print("   manager.optimize_library('merged_font')")
    
    # 分析字库
    print("\n4. 分析字库：")
    print("   analysis = manager.analyze_library('merged_font')")
    print("   print(f'字库包含 {analysis[\"total_characters\"]} 个字符')")
    
    # 列出字符
    print("\n5. 列出字库中的字符：")
    print("   characters = manager.list_characters('merged_font')")
    print("   print(f'字符列表：{characters}')")
    
    return manager

def example_text_recognition():
    """示例：文字识别"""
    print("\n=== 文字识别示例 ===")
    
    # 创建图色脚本实例
    script = ImageColorScript()
    
    # 加载字库
    print("1. 加载字库：")
    print("   script.load_font_library('my_font.json')")
    
    # 查找单个字符
    print("\n2. 查找单个字符：")
    print("   positions = script.find_character('字')")
    print("   if positions:")
    print("       x, y, w, h = positions[0]")
    print("       print(f'找到字符在位置：({x}, {y})')")
    
    # 查找字符中心坐标
    print("\n3. 查找字符中心坐标：")
    print("   center = script.find_character_center('字')")
    print("   if center:")
    print("       x, y = center")
    print("       script.click(x, y)  # 点击字符")
    
    # 查找文字串
    print("\n4. 查找文字串：")
    print("   text_positions = script.find_text('用户名')")
    print("   if text_positions:")
    print("       x, y, w, h = text_positions[0]")
    print("       print(f'找到文字串在位置：({x}, {y})')")
    
    # 在指定区域查找
    print("\n5. 在指定区域查找：")
    print("   region = (0, 0, 800, 600)  # 左上角800x600区域")
    print("   positions = script.find_character('字', region=region)")
    
    # 设置相似度阈值
    print("\n6. 设置相似度阈值：")
    print("   positions = script.find_character('字', threshold=0.9)")
    
    return script

def create_test_font_library():
    """创建测试用的字库"""
    print("\n=== 创建测试字库 ===")
    
    creator = FontLibraryCreator()
    
    # 创建一些简单的测试字符图像
    test_chars = {
        "A": create_letter_A(),
        "B": create_letter_B(),
        "1": create_digit_1(),
        "2": create_digit_2()
    }
    
    # 添加到字库
    for char, img in test_chars.items():
        creator.add_character_to_library(char, img, {"type": "test", "size": "medium"})
    
    # 保存测试字库
    if creator.save_library("test_font.json", "测试字库"):
        print("✓ 测试字库创建成功：test_font.json")
        return True
    
    return False

def create_letter_A():
    """创建字母A的简单图像"""
    img = np.zeros((30, 20), dtype=np.uint8)
    # 绘制字母A的简单形状
    cv2.line(img, (10, 25), (5, 5), 255, 2)   # 左边
    cv2.line(img, (10, 25), (15, 5), 255, 2)  # 右边
    cv2.line(img, (7, 15), (13, 15), 255, 2)  # 横线
    return img

def create_letter_B():
    """创建字母B的简单图像"""
    img = np.zeros((30, 20), dtype=np.uint8)
    # 绘制字母B的简单形状
    cv2.line(img, (5, 5), (5, 25), 255, 2)    # 左边竖线
    cv2.line(img, (5, 5), (12, 5), 255, 2)    # 上横线
    cv2.line(img, (5, 15), (12, 15), 255, 2)  # 中横线
    cv2.line(img, (5, 25), (12, 25), 255, 2)  # 下横线
    cv2.line(img, (12, 5), (12, 15), 255, 2)  # 上右竖线
    cv2.line(img, (12, 15), (12, 25), 255, 2) # 下右竖线
    return img

def create_digit_1():
    """创建数字1的简单图像"""
    img = np.zeros((30, 20), dtype=np.uint8)
    cv2.line(img, (10, 5), (10, 25), 255, 2)  # 竖线
    cv2.line(img, (8, 7), (10, 5), 255, 2)    # 左上角
    return img

def create_digit_2():
    """创建数字2的简单图像"""
    img = np.zeros((30, 20), dtype=np.uint8)
    cv2.line(img, (5, 8), (15, 8), 255, 2)    # 上横线
    cv2.line(img, (15, 8), (15, 15), 255, 2)  # 右竖线
    cv2.line(img, (15, 15), (5, 15), 255, 2)  # 中横线
    cv2.line(img, (5, 15), (5, 25), 255, 2)   # 左竖线
    cv2.line(img, (5, 25), (15, 25), 255, 2)  # 下横线
    return img

def complete_example():
    """完整的使用示例"""
    print("=== 完整使用示例 ===")
    
    try:
        # 1. 创建测试字库
        print("1. 创建测试字库...")
        if create_test_font_library():
            print("   ✓ 测试字库创建成功")
        else:
            print("   ✗ 测试字库创建失败")
            return
        
        # 2. 测试字库管理
        print("\n2. 测试字库管理...")
        manager = FontLibraryManager()
        if manager.load_library("test_font.json", "test"):
            print("   ✓ 字库加载成功")
            
            # 分析字库
            analysis = manager.analyze_library("test")
            print(f"   字库包含 {analysis.get('total_characters', 0)} 个字符")
            
            # 列出字符
            characters = manager.list_characters("test")
            print(f"   字符列表：{characters}")
        
        # 3. 测试文字识别
        print("\n3. 测试文字识别...")
        script = ImageColorScript()
        if script.load_font_library("test_font.json"):
            print("   ✓ 字库加载到识别引擎成功")
            print("   现在可以在屏幕上查找字符 A, B, 1, 2")
            
            # 示例：查找字符（需要屏幕上有对应字符）
            print("   使用方法：")
            print("   positions = script.find_character('A')")
            print("   center = script.find_character_center('1')")
            print("   text_pos = script.find_text('AB')")
        
        print("\n✓ 完整示例执行成功")
        print("\n使用提示：")
        print("1. 准备包含文字的图像文件")
        print("2. 使用 FontLibraryCreator 创建字库")
        print("3. 使用 FontLibraryManager 管理字库")
        print("4. 使用 ImageColorScript 进行文字识别")
        
    except Exception as e:
        print(f"完整示例执行失败：{e}")

if __name__ == "__main__":
    print("字库功能使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_create_font_library()
    example_manage_font_library()
    example_text_recognition()
    complete_example()
    
    print("\n" + "=" * 50)
    print("示例演示完成！")
    print("请根据实际需求修改代码中的文件路径和参数。")
